﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "Core/Lobby/LobbyPlayerController.h"

#include "Blueprint/UserWidget.h"
#include "Components/GameplayVoiceChatComponent.h"
#include "Core/StormEscapeGameInstance.h"
#include "Core/Lobby/LobbyGameState.h"

void ALobbyPlayerController::KickToMainMenu_Implementation(const FString& Reason)
{
	IControllerInterface::KickToMainMenu_Implementation(Reason);

	if (IsLocalController())
	{
		if (UStormEscapeGameInstance* GI = UStormEscapeGameInstance::GetActive_GameInstance(this))
		{
			GI->KickAndReturnToMenu(Reason);
		}
	}
	else
	{
		Client_KickToMainMenu(Reason);
	}

}

void ALobbyPlayerController::BeginPlay()
{
	Super::BeginPlay();

}

void ALobbyPlayerController::Client_KickToMainMenu_Implementation(const FString& Reason)
{
	UStormEscapeGameInstance* GI = UStormEscapeGameInstance::GetActive_GameInstance(this);
	if (GI)
	{
		GI->KickAndReturnToMenu(Reason);
	}
}
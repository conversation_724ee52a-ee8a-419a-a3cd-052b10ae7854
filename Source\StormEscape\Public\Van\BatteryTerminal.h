#pragma once

#include "CoreMinimal.h"
#include "Items/BaseItem.h"
#include "Interfaces/RepairInterface.h"
#include "BatteryTerminal.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnTerminalInteracted, bool, bLoose);

UCLASS()
class STORMESCAPE_API ABatteryTerminal : public ABaseItem, public IRepairInterface
{
	GENERATED_BODY()
	
public:
	ABatteryTerminal();

	void Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	FORCEINLINE bool RequiresTool_Implementation() override { return true; }

	FORCEINLINE bool IsSamePartType_Implementation(EVehiclePart InVehiclePart) override { return EVehiclePart::None == InVehiclePart; }

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	FOnTerminalInteracted OnTerminalInteractedDelegate;

	UPROPERTY(Replicated)
	bool bHasBatteryInstalled;

protected:
	virtual void BeginPlay() override;

	virtual void Complete_Implementation() override;

private:
	UPROPERTY(Replicated)
	bool bIsLoose;
};

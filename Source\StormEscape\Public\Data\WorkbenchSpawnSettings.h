// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DataAsset.h"
#include "Items/ItemSpawnTypes.h"
#include "UtilityTypes.h"
#include "WorkbenchSpawnSettings.generated.h"

UENUM(BlueprintType)
enum class EWorkbenchZone : uint8
{
	Pegboard = 0	UMETA(DisplayName = "Pegboard"),
	Table = 1		UMETA(DisplayName = "Table"),
	UnderTable = 2	UMETA(DisplayName = "UnderTable")
};

UCLASS()
class STORMESCAPE_API UWorkbenchSpawnSettings : public UPrimaryDataAsset
{
	GENERATED_BODY()
	
public:
	UWorkbenchSpawnSettings();

	int GetItemCount(int numPlayers) const { return itemCount.Contains(numPlayers) ? itemCount[numPlayers].GetValue() : 0; }

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Zones")
	FItemBenchData pegboardSettings;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Zones")
	FItemBenchData tableSettings;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Zones")
	FItemBenchData underTableSettings;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, meta = (ReadOnlyKeys, ShowOnlyInnerProperties))
	TMap<int, FIntegerInterval> itemCount;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category="Items", meta = (ShowOnlyInnerProperties))
	TMap<FName, FItemSpawnData> itemPool;
};

// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "Voice/VoiceImportantTypes.h"
#include "OdinSynthComponent.h"
#include "OdinAudioGeneratorLoopbackComponent.h"
#include "VoiceEmitterInterface.generated.h"

// This class does not need to be modified.
UINTERFACE(MinimalAPI)
class UVoiceEmitterInterface : public UInterface
{
	GENERATED_BODY()
};

class STORMESCAPE_API IVoiceEmitterInterface
{
	GENERATED_BODY()

	// Add interface functions to this class. This is the class that will be inherited to implement this interface.
public:
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	UOdinSynthComponent* CreateVoiceSynthComponent(const FVoiceRoomData& roomData, int64 peerID, UOdinPlaybackMedia* media);

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	UOdinAudioGeneratorLoopbackComponent* CreateVoiceLoopbackComponent(const FVoiceRoomData& roomData, int64 peerID);
};

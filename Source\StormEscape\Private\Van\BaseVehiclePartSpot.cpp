#include "Van/BaseVehiclePartSpot.h"
#include "Components/InteractComponent.h"
#include "Van/GameplayVan.h"
#include "Net/UnrealNetwork.h"

ABaseVehiclePartSpot::ABaseVehiclePartSpot()
{
	PrimaryActorTick.bCanEverTick = true;
	bReplicates = true;

	spotMesh = CreateDefaultSubobject<UStaticMeshComponent>("Mesh");
	SetRootComponent(spotMesh);
}

void ABaseVehiclePartSpot::BeginPlay()
{
	Super::BeginPlay();
	
	FTimerHandle timer;
	GetWorld()->GetTimerManager().SetTimer(timer, this, &ABaseVehiclePartSpot::GetVanReference, 0.1f, false);
}

void ABaseVehiclePartSpot::GetVanReference()
{
	if (GetOwner())
	{
		vanRef = Cast<AGameplayVan>(GetOwner());
	}
}

void ABaseVehiclePartSpot::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

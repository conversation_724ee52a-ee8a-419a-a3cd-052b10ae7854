﻿#include "Core/Lobby/LobbyGameMode.h"
#include "AdvancedSessionsLibrary.h"
#include "EngineUtils.h"
#include "Core/Lobby/LobbyGameState.h"
#include "Core/Lobby/LobbyPlayerController.h"
#include "Core/Lobby/LobbyPlayerState.h"
#include "Data/PDA_GameplayMaps.h"
#include "GameFramework/PlayerStart.h"
#include "Interfaces/ControllerInterface.h"

void ALobbyGameMode::BeginPlay()
{
	Super::BeginPlay();

	// Initialize the session interface
	CachedSessionInterface = Online::GetSessionInterface(GetWorld());

	// Set initial full lobby state
	bFullLobby = (GetCurrentPlayers() >= GetMaxPlayers());
}

AActor* ALobbyGameMode::ChoosePlayerStart_Implementation(AController* Player)
{
	if (Player)
	{
		// Check if the player is the host
		if (Player->IsLocalController())
		{
			// Find the player start tagged with "Host"
			for (APlayerStart* PlayerStart : TActorRange<APlayerStart>(GetWorld()))
			{
				if (PlayerStart->PlayerStartTag == FName("Host"))
				{
					return PlayerStart;
				}
			}
		}
		else
		{
			// Find a player start for clients based on their index
			int32 ClientIndex = GetCurrentPlayers();
			FString TagName = FString::Printf(TEXT("Client%d"), ClientIndex);
			for (APlayerStart* PlayerStart : TActorRange<APlayerStart>(GetWorld()))
			{
				if (PlayerStart->PlayerStartTag == FName(*TagName))
				{
					return PlayerStart;
				}
			}
		}
	}

	// Default behavior if no specific player start is found
	return Super::ChoosePlayerStart_Implementation(Player);
}

void ALobbyGameMode::PreLogin(const FString& Options, const FString& Address, const FUniqueNetIdRepl& UniqueId, FString& ErrorMessage)
{
	Super::PreLogin(Options, Address, UniqueId, ErrorMessage);

	// Reject players if the match has already started
	if (bIsMatchInProgress)
	{
		ErrorMessage = TEXT("Match already started.");
		UE_LOG(LogTemp, Warning, TEXT("[PreLogin] Match in progress. Rejecting new player."));
		return;
	}

	// Reject players if the server is full
	if (GetCurrentPlayers() >= GetMaxPlayers())
	{
		ErrorMessage = TEXT("Server is full.");
		UE_LOG(LogTemp, Warning, TEXT("[PreLogin] Server full. Rejecting connection."));
	}
}

void ALobbyGameMode::PostLogin(APlayerController* NewPlayer)
{
	Super::PostLogin(NewPlayer);

	// Handle remote players
	if (!NewPlayer->IsLocalController())
	{
		// Kick player if the match has already started
		if (bIsMatchInProgress)
		{
			UE_LOG(LogTemp, Warning, TEXT("[PostLogin] Match already started. Kicking player."));
			if (IControllerInterface* ControllerInterface = Cast<IControllerInterface>(NewPlayer))
			{
				ControllerInterface->Execute_KickToMainMenu(NewPlayer,"Match already started.");
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("[PostLogin] Player does not implement IControllerInterface."));
			}
			return;
		}

		// Kick player if the server is full
		if (GetCurrentPlayers() >= GetMaxPlayers())
		{
			UE_LOG(LogTemp, Warning, TEXT("[PostLogin] Server full post-login. Kicking player."));
			if (IControllerInterface* ControllerInterface = Cast<IControllerInterface>(NewPlayer))
			{
				ControllerInterface->Execute_KickToMainMenu(NewPlayer,"Server is full.");
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("[PostLogin] Player does not implement IControllerInterface."));
			}
			return;
		}

		// Increment the current player count
		UpdateCurrentPlayers(GetCurrentPlayers() + 1);

		// Update full lobby state
		bFullLobby = (GetCurrentPlayers() >= GetMaxPlayers());
	}

	// Update lobby state with the new player's information
	if (ALobbyGameState* LobbyState = GetGameState<ALobbyGameState>())
	{
		if (APlayerState* PlayerState = NewPlayer->GetPlayerState<APlayerState>())
		{
			FCrewMember NewInfo;
			NewInfo.PlayerName = PlayerState->GetPlayerName();
			LobbyState->AddPlayerInfo(NewInfo);
		}
	}
}

void ALobbyGameMode::Logout(AController* Exiting)
{
	Super::Logout(Exiting);

	// Skip updating player count if the match is in progress
	if (bIsMatchInProgress)
	{
		UE_LOG(LogTemp, Warning, TEXT("[Logout] Match in progress. Not updating player count."));
		return;
	}

	// Remove player information from the lobby state
	if (ALobbyGameState* GS = GetGameState<ALobbyGameState>())
	{
		if (APlayerState* LeavingPlayerState = Exiting->GetPlayerState<APlayerState>())
		{
			GS->RemovePlayerInfoByName(LeavingPlayerState->GetPlayerName());
		}
	}

	// Decrement the current player count
	UpdateCurrentPlayers(FMath::Max(0, GetCurrentPlayers() - 1));

	// Update full lobby state
	bFullLobby = (GetCurrentPlayers() >= GetMaxPlayers());
}

void ALobbyGameMode::KickPlayerByName(const FString& PlayerNameToKick) const
{
	for (FConstPlayerControllerIterator Iterator = GetWorld()->GetPlayerControllerIterator(); Iterator; ++Iterator)
	{
		APlayerController* PC = Iterator->Get();
		if (!PC || !PC->PlayerState) continue;

		const FString& CurrentPlayerName = PC->PlayerState->GetPlayerName();
		if (CurrentPlayerName.Equals(PlayerNameToKick, ESearchCase::IgnoreCase))
		{
			UE_LOG(LogTemp, Warning, TEXT("Kicking Player: %s"), *CurrentPlayerName);

			// Notify the player they are being kicked
			if (IControllerInterface* ControllerInterface = Cast<IControllerInterface>(PC))
			{
				ControllerInterface->Execute_KickToMainMenu(PC,"You have been kicked from the lobby.");
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("Player %s does not implement IControllerInterface."), *CurrentPlayerName);
			}
			return;
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("Player not found: %s"), *PlayerNameToKick);
}

void ALobbyGameMode::StartMatch()
{
	if (ALobbyGameState* GS = GetGameState<ALobbyGameState>())
	{
		FPlayMapInfo CurrentPlayMap = GS->CurrentPlayMap;

		if (!CurrentPlayMap.TravelAddress.IsEmpty())
		{
			// Mark the match as in progress and hide the session
			bIsMatchInProgress = true;

			// Update the session state to "InProgress"
			if (FNamedOnlineSession* Session = GetSession())
			{
				Session->SessionState = EOnlineSessionState::InProgress;
				Session->SessionSettings.bAllowJoinInProgress = false;
				CachedSessionInterface->UpdateSession("StormEscape", Session->SessionSettings, true);
			}

			OnMatchStarted.Broadcast();			

			// Travel to the selected map
			const FString FullTravelURL = CurrentPlayMap.TravelAddress + "?listen";
			GetWorld()->ServerTravel(FullTravelURL);
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("StartMatch: No valid map selected!"));
		}
	}
}

FNamedOnlineSession* ALobbyGameMode::GetSession() const
{
	if (!CachedSessionInterface.IsValid()) return nullptr;
	return CachedSessionInterface->GetNamedSession("StormEscape");
}

int32 ALobbyGameMode::GetCurrentPlayers() const
{
	int32 Count = 0;
	if (const FNamedOnlineSession* Session = GetSession())
	{
		Session->SessionSettings.Get(TEXT("CurrentPlayers"), Count);
	}
	return Count;
}

int32 ALobbyGameMode::GetMaxPlayers() const
{
	if (const FNamedOnlineSession* Session = GetSession())
	{
		return Session->SessionSettings.NumPublicConnections;
	}
	return 0;
}

void ALobbyGameMode::UpdateCurrentPlayers(int32 NewCount) const
{
	if (FNamedOnlineSession* Session = GetSession())
	{
		Session->SessionSettings.Set(TEXT("CurrentPlayers"), NewCount, EOnlineDataAdvertisementType::ViaOnlineServiceAndPing);
		CachedSessionInterface->UpdateSession("StormEscape", Session->SessionSettings, true);
	}
	// Note: bFullLobby is updated in PostLogin/Logout after calling this
}

#include "Items/BaseVehiclePart.h"
#include "Camera/CameraComponent.h"
#include "Components/InteractComponent.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Van/GameplayVan.h"
#include "Van/BaseVehiclePartSpot.h"

ABaseVehiclePart::ABaseVehiclePart()
{
	bIsRemovable = false;
	bIsDamaged = false;
	bRequiresToolBox = false;
	actionDuration = 0.f;
	timeToInsert = 2.f;
	timeToInstall = 2.f;
	timeToRemove = 2.f;
	activationType = EActivationType::Hold;
	progressType = EProgressType::Persistent;
}

#if WITH_EDITOR
void ABaseVehiclePart::OnConstruction(const FTransform& Transform)
{
	Super::OnConstruction(Transform);

	if (itemMesh)
	{
		if (bIsPickable)
		{
			itemMesh->SetSimulatePhysics(true);
		}

		if (baseMaterial && damagedMaterial)
		{
			if (!bIsDamaged)
				itemMesh->SetMaterial(0, baseMaterial);
			else
				itemMesh->SetMaterial(0, damagedMaterial);
		}
	}
}
#endif

void ABaseVehiclePart::BeginPlay()
{
	Super::BeginPlay();

	FTimerHandle timer;
	GetWorld()->GetTimerManager().SetTimer(timer, this, &ABaseVehiclePart::GetVanReference, 0.1f, false);

	if(bIsRemovable)
		GetSpot();

	if (bIsDamaged)
		SetActionDuration(timeToRemove);
}

void ABaseVehiclePart::GetSpot()
{
	if (!itemMesh)
		return;

	FHitResult hitResult;
	FVector startLocation = itemMesh->GetComponentLocation();
	FVector endLocation = startLocation;

	TArray<AActor*> actorsToIgnore = { this, GetOwner() };
	ETraceTypeQuery traceChannel = UEngineTypes::ConvertToTraceType(ECC_Visibility);

	bool bHit = UKismetSystemLibrary::SphereTraceSingle(
		GetWorld(),
		startLocation,
		endLocation,
		10.f,
		traceChannel,
		false,
		actorsToIgnore,
		EDrawDebugTrace::None,
		hitResult,
		true
	);

	if (bHit)
	{
		if (hitResult.GetActor())
		{
			ABaseVehiclePartSpot* hitActor = Cast<ABaseVehiclePartSpot>(hitResult.GetActor());
			if (hitActor)
			{
				if (IRepairInterface::Execute_IsSamePartType(hitActor, vehiclePart))
				{
					vehiclePartSpot = hitActor;
				}
			}
		}
	}
}

void ABaseVehiclePart::GetVanReference()
{
	if (GetOwner())
	{
		vanRef = Cast<AGameplayVan>(GetOwner());
	}
}

void ABaseVehiclePart::Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	if (interactor && interactComponent)
	{
		if (bIsHeld)
		{
			userComponent = interactComponent;
			TraceVehiclePartSpot(userComponent);
		}
		else 
		{
			Super::Interact_Implementation(interactor, interactComponent);
		}
	}
}

void ABaseVehiclePart::TraceVehiclePartSpot(UInteractComponent* interactComponent)
{
	FHitResult hitResult;

	if (interactComponent->GetTraceResult(180.f, hitResult))
	{
		if (hitResult.GetActor())
		{
			ABaseVehiclePartSpot* hitActor = Cast<ABaseVehiclePartSpot>(hitResult.GetActor());
			if (hitActor)
			{
				if (IRepairInterface::Execute_IsSamePartType(hitActor, vehiclePart))
				{
					vehiclePartSpot = hitActor;
					SetActionDuration(timeToInsert);
					Activate();
					interactComponent->bIsInteracting = true;
				}
			}
		}
	}
}

void ABaseVehiclePart::Complete_Implementation()
{
	Super::Complete_Implementation();

	if (bIsDamaged && !bIsHeld)
	{
		RemovePart();
	}
	else if (!bIsDamaged && bIsHeld && vehiclePartSpot && userComponent)
	{
		InsertPart();
	}
	else if (!bIsDamaged && !bIsHeld && bRequiresToolBox)
	{
		InstallPart();
	}
}

void ABaseVehiclePart::RemovePart()
{
	if (vanRef)
		vanRef->OnPartRemovedDelegate.Broadcast(vehiclePart);

	userComponent->OnFocusItemChangedDelegate.Broadcast(nullptr);
	Destroy();
	
}

void ABaseVehiclePart::InsertPart()
{
	userComponent->DropItem(false);
	SetOwner(vehiclePartSpot->GetOwner());
	Tags.Add(vehiclePartSpot->Tags[0]);
	SwapVehicleParts(vehiclePartSpot->spotMesh);
	userComponent = nullptr;

	bActivated = false;
	progress = 0.f;
	OnRep_ProgressUpdate();
}

void ABaseVehiclePart::InstallPart()
{
	ApplyTransparency(false);
	SetActionDuration(0.f);
	if (vanRef)
		vanRef->OnPartInstalledDelegate.Broadcast(vehiclePart);
}

void ABaseVehiclePart::SwapVehicleParts(UPrimitiveComponent* InVehiclePartSpot)
{
	if (InVehiclePartSpot)
	{
		bIsPickable = false;
		GetVanReference();
		Multicast_SwapVehicleParts(InVehiclePartSpot);

		if (bRequiresToolBox)
		{
			ApplyTransparency(true);
			SetActionDuration(timeToInstall);
		}
		else
		{
			InstallPart();
		}	
	}
}

void ABaseVehiclePart::Multicast_SwapVehicleParts_Implementation(UPrimitiveComponent* InVehiclePartSpot)
{
	itemMesh->SetSimulatePhysics(false);
	FAttachmentTransformRules attachRules(
		EAttachmentRule::SnapToTarget,
		EAttachmentRule::SnapToTarget,
		EAttachmentRule::KeepWorld,
		false
	);
	itemMesh->AttachToComponent(InVehiclePartSpot, attachRules);
}

void ABaseVehiclePart::ApplyTransparency_Implementation(bool bShouldApply)
{
}

void ABaseVehiclePart::Interrupt_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	Super::Interrupt_Implementation(interactor, interactComponent);

	if (interactor && interactComponent)
	{
		if (bIsHeld)
		{
			interactComponent->StopInteraction();
			vehiclePartSpot = nullptr;
			SetActionDuration(0.f);
		}
	}
}
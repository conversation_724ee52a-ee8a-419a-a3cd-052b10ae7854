#pragma once

#include "UtilityTypes.generated.h"

USTRUCT(BlueprintType)
struct FIntegerInterval
{
	GENERATED_BODY()

public:
	FIntegerInterval() : min (0), max (1) {}
	FIntegerInterval(int inMin, float inMax) : min(inMin), max(inMax) {}

	int GetValue() const { return FMath::RandRange(min, max); }

protected:
	UPROPERTY(EditAnywhere)
	int min;

	UPROPERTY(EditAnywhere)
	int max;
};

typedef union ticketValue {
	bool bValid;
	FName name;
	uint8 baseEnum;

	ticketValue() : bValid(false) {}
	ticketValue(FName inName) : name(inName) {}
	ticketValue(uint8 inEnum) : baseEnum(inEnum) {}

} TTicketValue;

USTRUCT(BlueprintType)
struct FRouletteTicket
{
	GENERATED_BODY()

public:
	FRouletteTicket() : value(), id(INDEX_NONE), weight(0.f), entry(0.f), bValid(false) {}
	FRouletteTicket(FName inName, int inId, float inWeight, float inEntry) : value(inName), id(inId), weight(inWeight), entry(inEntry), bValid(true) {}
	FRouletteTicket(uint8 inEnum, int inId, float inWeight, float inEntry) : value(inEnum), id(inId), weight(inWeight), entry(inEntry), bValid(true) {}

	TTicketValue value;

	int id;

	UPROPERTY(BlueprintReadOnly)
	float weight;

	float entry;

	bool bValid;

};

USTRUCT(BlueprintType)
struct FRoulette
{
	GENERATED_BODY()

public:
	FRoulette() : totalWeight(0.f) {}

	int AddTicket(FName name, float weight = 1.f)
	{
		totalWeight += weight;
		int id = tickets.Num();
		tickets.Add(FRouletteTicket(name, id, weight, totalWeight));
		return id;
	}

	int AddTicket(uint8 value, float weight = 1.f)
	{
		totalWeight += weight;
		int id = tickets.Num();
		tickets.Add(FRouletteTicket(value, id, weight, totalWeight));
		return id;
	}

	void RemoveTicket(int id)
	{
		if (!tickets.IsValidIndex(id))
		{
			return;
		}

		tickets[id].bValid = false;
		for (int i = id; i < tickets.Num(); i++)
		{
			tickets[i].entry -= tickets[id].weight;
		}
	}

	void Clear()
	{
		totalWeight = 0.f;
		tickets.Empty();
	}

	FRouletteTicket Spin() const
	{
		float needle = FMath::RandRange(0.f, totalWeight);
		FRouletteTicket lasValidTicket = FRouletteTicket();
		for (const FRouletteTicket& ticket : tickets)
		{
			if (!ticket.bValid)
			{
				continue;
			}

			lasValidTicket = ticket;
			if (needle <= ticket.entry)
			{
				return ticket;
			}
		}

		return lasValidTicket;
	}

protected:
	float totalWeight;

	TArray<FRouletteTicket> tickets;
};


#pragma once

#include "CoreMinimal.h"
#include "Items/BaseItem.h"
#include "Oil.generated.h"

class AGameplayVan;
class AOilCap;

UCLASS()
class STORMESCAPE_API AOil : public ABaseItem
{
	GENERATED_BODY()

public:
	AOil();

	void Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	void Interrupt_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	UFUNCTION(BlueprintCallable, BlueprintPure)
	float GetCurrentOil();

protected:
	virtual void BeginPlay() override;

	virtual void Process_Implementation(float deltaSeconds) override;

	virtual void Deactivate_Implementation() override;

	void NotifyDrop_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties", meta = (ClampMin = "1.0", ClampMax = "6.0"))
	float amountOfOil;

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void UpdateWidget(float value);
private:
	AGameplayVan* vanRef;

	AOilCap* oilCap;

	void TraceCap(const bool bOnlyTrace);

	void UpdateWeight();
};

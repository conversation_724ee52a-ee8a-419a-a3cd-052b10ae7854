// Fill out your copyright notice in the Description page of Project Settings.


#include "Core/StormEscapeGameMode.h"

#include "OnlineSessionSettings.h"
#include "OnlineSubsystem.h"
#include "Weather/WeatherSubsystem.h"
#include "StormEscapePlayerController.h"
#include "Interfaces/OnlineSessionInterface.h"

void AStormEscapeGameMode::StartPlay()
{
	Super::StartPlay();

	UWeatherSubsystem* weather = GetWorld()->GetSubsystem<UWeatherSubsystem>();
	weather->SpawnTornado(tornadoClass);
}

void AStormEscapeGameMode::PostLogin(APlayerController* NewPlayer)
{
	Super::PostLogin(NewPlayer);

	if (AStormEscapePlayerController* Controller = Cast<AStormEscapePlayerController>(NewPlayer))
	{
		Controller->OnPlayerDeathDelegate.AddUniqueDynamic(this, &AStormEscapeGameMode::OnPlayerDeath);
		UE_LOG(LogGameMode, Log, TEXT("%s Joined"), *Controller->GetName())
	}
}

void AStormEscapeGameMode::Logout(AController* Exiting)
{
	if (AStormEscapePlayerController* Controller = Cast<AStormEscapePlayerController>(Exiting))
	{
		Controller->OnPlayerDeathDelegate.RemoveAll(this);
		UE_LOG(LogGameMode, Log, TEXT("%s Left"), *Controller->GetName())
	}

	Super::Logout(Exiting);
}

void AStormEscapeGameMode::OnPlayerDeath(APlayerController* Player)
{
	UE_LOG(LogGameMode, Log, TEXT("%s Died"), *Player->GetName())
}

void AStormEscapeGameMode::BeginPlay()
{
	Super::BeginPlay();

	
}

// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "CameraShakeComponent.generated.h"

class UCharacterMovementComponent;
class ULegacyCameraShake;

UENUM(BlueprintType)
enum class EShakeType : uint8
{
	Idle,
	Walking,
	Running,
	Jumping,
	Landing,
	HardLanding,
	HardDamage,
	SoftDamage
};

UCLASS( ClassGroup=(Custom), meta=(BlueprintSpawnableComponent) )
class STORMESCAPE_API UCameraShakeComponent : public UActorComponent
{
	GENERATED_BODY()

public:	
	UCameraShakeComponent();

	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	UFUNCTION(BlueprintCallable)
	void StartShake(EShakeType shakeType, float scale = 1.f);

	UFUNCTION(BlueprintCallable)
	FORCEINLINE void EnableAllShakes(bool bShouldShake) { bIsActive = bShouldShake; }

protected:
	virtual void BeginPlay() override;

	UPROPERTY(EditAnywhere, Category = "Head Bobs")
	TSubclassOf<ULegacyCameraShake> idleShake;

	UPROPERTY(EditAnywhere, Category = "Head Bobs")
	TSubclassOf<ULegacyCameraShake> walkingShake;

	UPROPERTY(EditAnywhere, Category = "Head Bobs")
	TSubclassOf<ULegacyCameraShake> runningShake;

	UPROPERTY(EditAnywhere, Category = "Head Bobs")
	TSubclassOf<ULegacyCameraShake> jumpShake;

	UPROPERTY(EditAnywhere, Category = "Head Bobs")
	TSubclassOf<ULegacyCameraShake> landShake;

	UPROPERTY(EditAnywhere, Category = "Head Bobs")
	TSubclassOf<ULegacyCameraShake> hardLandShake;

	UPROPERTY(EditAnywhere, Category = "Head Bobs")
	TSubclassOf<ULegacyCameraShake> HardDamageShake;

	UPROPERTY(EditAnywhere, Category = "Head Bobs")
	TSubclassOf<ULegacyCameraShake> SoftDamageShake;

private:
	bool bIsActive = true;

	ACharacter* characterRef;
	APlayerController* playerController;
	UCharacterMovementComponent* movementComponent;
	TSubclassOf<ULegacyCameraShake> lastShake;
	FTimerHandle shakeHandle;

	float shakeIntensity;
	float currentIntensity;

	void ExecuteShake(TSubclassOf<ULegacyCameraShake> shakeClass, const float scale);
	void PerformIdle();

	UFUNCTION()
	void IncreaseShakeIntensity();
};

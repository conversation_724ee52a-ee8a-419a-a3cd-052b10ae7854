#pragma once

#include "CoreMinimal.h"
#include "Van/BaseVehiclePartSpot.h"
#include "FuseSpot.generated.h"

UCLASS()
class STORMESCAPE_API AFuseSpot : public ABaseVehiclePartSpot
{
	GENERATED_BODY()
	
public:
	AFuseSpot();

	UFUNCTION(BlueprintCallable, BlueprintPure)
	FORCEINLINE UPrimitiveComponent* GetFuseAttachment() { return fuseAttachMesh; }

protected:
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UStaticMeshComponent* fuseAttachMesh;
};

#include "BaseFirstPersonCharacter.h"
#include "InputMappingContext.h"
#include "EnhancedInputSubsystems.h"
#include "EnhancedInputComponent.h"
#include "StormEscapePlayerController.h"
#include "Camera/CameraComponent.h"
#include "Components/GameplayVoiceChatComponent.h"
#include "Core/StormEscapeGameInstance.h"
#include "GameFramework/SpringArmComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Kismet/GameplayStatics.h"
#include "Net/UnrealNetwork.h"
#include "PhysicsEngine/PhysicsConstraintComponent.h"
#include "Core/StormEscapeGameState.h"
#include "Components/CapsuleComponent.h"
#include "UI/StormEscapeHUD.h"

ABaseFirstPersonCharacter::ABaseFirstPersonCharacter()
{
	PrimaryActorTick.bCanEverTick = true;

	cameraShakeComponent = CreateDefaultSubobject<UCameraShakeComponent>("Camera Shake Component");
	cameraShakeComponent->PrimaryComponentTick.bCanEverTick = false;

	attributes = CreateDefaultSubobject<UCharacterAttributesComponent>("Attributes Component");
	interactComponent = CreateDefaultSubobject<UInteractComponent>("Interact Component");

	springArm = CreateDefaultSubobject<USpringArmComponent>("SpringArm");
	springArm->SetupAttachment(GetMesh(), FName("head"));
	springArm->bUsePawnControlRotation = true;
	springArm->TargetArmLength = 0.f;
	springArm->bEnableCameraRotationLag = true;
	springArm->CameraRotationLagSpeed = 30.f;

	camera = CreateDefaultSubobject<UCameraComponent>("Camera");
	camera->SetupAttachment(springArm);

	heldItemSlot = CreateDefaultSubobject<UStaticMeshComponent>("Held Item Slot");
	heldItemSlot->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
	heldItemSlot->SetupAttachment(camera);
	heldItemSlot->bHiddenInGame = true;

	grabConstraint = CreateDefaultSubobject<UPhysicsConstraintComponent>("Grab Constraint");
	grabConstraint->SetupAttachment(heldItemSlot);
	grabConstraint->SetLinearXLimit(ELinearConstraintMotion::LCM_Free, 0.f);
	grabConstraint->SetLinearYLimit(ELinearConstraintMotion::LCM_Free, 0.f);
	grabConstraint->SetLinearZLimit(ELinearConstraintMotion::LCM_Free, 0.f);
	grabConstraint->SetLinearPositionDrive(true, true, true);
	grabConstraint->SetLinearVelocityDrive(true, true, true);
	grabConstraint->SetLinearDriveParams(250.f, 30.f, 0.f);
	grabConstraint->SetAngularDriveMode(EAngularDriveMode::TwistAndSwing);
	grabConstraint->SetAngularOrientationDrive(true, true);
	grabConstraint->SetAngularVelocityDrive(true, true);
	grabConstraint->SetAngularDriveParams(1000.f, 100.f, 0.f);

	bUseControllerRotationYaw = true;
	GetCharacterMovement()->bOrientRotationToMovement = false;
	GetCharacterMovement()->MaxWalkSpeed = 200.f;
	GetCharacterMovement()->MaxWalkSpeedCrouched = 100.f;
	GetCharacterMovement()->GetNavAgentPropertiesRef().bCanCrouch = true;

	GetCapsuleComponent()->SetCollisionResponseToChannel(ECollisionChannel::ECC_Pawn, ECollisionResponse::ECR_Overlap);
}

void ABaseFirstPersonCharacter::OnRep_PlayerId()
{
	UStormEscapeGameInstance* GameInstance = UStormEscapeGameInstance::GetActive_GameInstance(this);
	GameInstance->RegisterCharacterToGuid(PlayerId,this);
	UE_LOG(LogTemp, Warning, TEXT("Replicated PlayerId: %s"), *PlayerId.ToString());

	if (IsLocallyControlled())
	{
		GetController()->GetComponentByClass<UGameplayVoiceChatComponent>()->TryStartVoiceChat(PlayerId);
	}

}

void ABaseFirstPersonCharacter::BeginPlay()
{
	Super::BeginPlay();

	InitializePlayer();
}

void ABaseFirstPersonCharacter::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	if (IsPlayerDead())
	{
		Multicast_UpdateCapsule(FVector_NetQuantize::Zero());
	}
	else
	{
		PerformIdleShake();

		if (GetCharacterMovement()->Velocity.Length() == 0.f)
		{
			TArray<AActor*> ovelappingActors;
			GetCapsuleComponent()->GetOverlappingActors(ovelappingActors, GetClass());
			if (ovelappingActors.Num() > 0)
			{
				PushPlayerOffCollision(ovelappingActors, DeltaTime);
			}
		}
	}
}

void ABaseFirstPersonCharacter::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	Super::SetupPlayerInputComponent(PlayerInputComponent);

	if (UEnhancedInputComponent* input = CastChecked<UEnhancedInputComponent>(PlayerInputComponent))
	{
		if (moveAction)
		{
			input->BindAction(moveAction, ETriggerEvent::Triggered, this, &ABaseFirstPersonCharacter::Move);
			input->BindAction(moveAction, ETriggerEvent::Completed, this, &ABaseFirstPersonCharacter::StopSprinting);
		}
		if (sprintAction)
		{
			input->BindAction(sprintAction, ETriggerEvent::Started, this, &ABaseFirstPersonCharacter::Sprint);
			input->BindAction(sprintAction, ETriggerEvent::Completed, this, &ABaseFirstPersonCharacter::StopSprinting);
		}
		if (crouchAction)
		{
			input->BindAction(crouchAction, ETriggerEvent::Started, this, &ABaseFirstPersonCharacter::PerformCrouch);
			input->BindAction(crouchAction, ETriggerEvent::Completed, this, &ABaseFirstPersonCharacter::StopCrouching);
		}
		if (lookAction)
			input->BindAction(lookAction, ETriggerEvent::Triggered, this, &ABaseFirstPersonCharacter::Look);
		if (jumpAction)
			input->BindAction(jumpAction, ETriggerEvent::Started, this, &ABaseFirstPersonCharacter::PerformJump);
		if (interactAction)
		{
			input->BindAction(interactAction, ETriggerEvent::Started, this, &ABaseFirstPersonCharacter::Interact);
			input->BindAction(interactAction, ETriggerEvent::Completed, this, &ABaseFirstPersonCharacter::StopInteracting);
		}
		if (dropItemAction)
			input->BindAction(dropItemAction, ETriggerEvent::Started, this, &ABaseFirstPersonCharacter::DropItem);
		if (itemAction)
		{
			input->BindAction(itemAction, ETriggerEvent::Started, this, &ABaseFirstPersonCharacter::TriggerItemAction);
			input->BindAction(itemAction, ETriggerEvent::Completed, this, &ABaseFirstPersonCharacter::StopItemAction);
		}
	}
}

void ABaseFirstPersonCharacter::InitializePlayer()
{
	if (IsLocallyControlled() && IsControlled())
	{
		SetInputMapping();
		StartCameraFade(true);
		ApplyOwnerMaterials();
	}

	if (interactComponent)
	{
		interactComponent->InitializeInteractionComponent(camera, heldItemSlot, grabConstraint, false);
		interactComponent->OnSpeedModifierChangedDelegate.AddUniqueDynamic(this, &ABaseFirstPersonCharacter::OnSpeedChangedByInteraction);
	}

	if (playerController)
	{
		//DisableInput(playerController);

		if (AStormEscapeGameState* gameState = GetWorld()->GetGameState<AStormEscapeGameState>())
		{
			gameState->OnAllPlayersSpawnedDelegate.AddUniqueDynamic(this, &ABaseFirstPersonCharacter::EnablePlayerInput);
		}
	}
	
	InitVoiceChat();
	
	bIsRagdolling = false;
}
void ABaseFirstPersonCharacter::SetInputMapping()
{
	if (inputMapping)
	{
		playerController = Cast<APlayerController>(Controller);
		if (playerController)
		{
			if (UEnhancedInputLocalPlayerSubsystem* subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(playerController->GetLocalPlayer()))
			{
				subsystem->AddMappingContext(inputMapping, 0);
			}
		}
	}
}

void ABaseFirstPersonCharacter::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME_CONDITION(ABaseFirstPersonCharacter, bIsCrouching, COND_OwnerOnly);
	DOREPLIFETIME_CONDITION(ABaseFirstPersonCharacter, bIsRunning, COND_OwnerOnly);
	DOREPLIFETIME(ABaseFirstPersonCharacter, PlayerId);
	DOREPLIFETIME(ABaseFirstPersonCharacter, healthStatus);
}

void ABaseFirstPersonCharacter::EnablePlayerInput()
{
	if (playerController)
	{
		EnableInput(playerController);
	}
}

void ABaseFirstPersonCharacter::InitVoiceChat()
{
	if (GetNetMode() == NM_DedicatedServer || GetNetMode() == NM_ListenServer)
	{
		PlayerId = FGuid::NewGuid();

		UStormEscapeGameInstance* GameInstance = UStormEscapeGameInstance::GetActive_GameInstance(this);
		GameInstance->RegisterCharacterToGuid(PlayerId,this);

		UE_LOG(LogTemp, Warning, TEXT("Created PlayerId: %s"), *PlayerId.ToString());

		if (IsLocallyControlled())
		{
			if (UActorComponent* VoiceChatComp = Controller->GetComponentByClass(UGameplayVoiceChatComponent::StaticClass()))
			{
				Cast<UGameplayVoiceChatComponent>(VoiceChatComp)->TryStartVoiceChat(PlayerId);
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("Voice chat not found"));
			}
		}
	}
}

void ABaseFirstPersonCharacter::ApplyOwnerMaterials()
{
	if (owningPlayerMaterials.Num() > 0 && GetMesh())
	{
		for (int32 i = 0; i < owningPlayerMaterials.Num(); i++)
		{
			if (owningPlayerMaterials[i])
			{
				GetMesh()->SetMaterial(i, owningPlayerMaterials[i]);
			}
		}
	}
}

void ABaseFirstPersonCharacter::PerformIdleShake()
{
	if (cameraShakeComponent && attributes && GetCharacterMovement()->Velocity.Length() <= 10.f && GetCharacterMovement()->CanEverJump())
	{
		float multiplier = FMath::GetMappedRangeValueClamped(FVector2D(attributes->maxStamina, 0.f), FVector2D(1.f, 3.f), attributes->stamina);
		cameraShakeComponent->StartShake(EShakeType::Idle, multiplier);
	}
}

void ABaseFirstPersonCharacter::UpdateMovementSpeed()
{
	if (IsValid(attributes))
	{
		float baseSpeed = bIsRunning ? attributes->runningSpeed : attributes->walkingSpeed;
		GetCharacterMovement()->MaxWalkSpeed = baseSpeed * GetSpeedMultiplier();
	}
}

void ABaseFirstPersonCharacter::OnSpeedChangedByInteraction(float speedModifier)
{
	UpdateMovementSpeed();
	K2_OnSpeedMultiplierChanged(1.f + speedModifier);
}

float ABaseFirstPersonCharacter::GetSpeedMultiplier() const
{
	float modifier = IsValid(interactComponent) ? interactComponent->GetSpeedModifier() : 0.f;
	return 1.f + modifier;
}

void ABaseFirstPersonCharacter::Move(const FInputActionValue& inputValue)
{
	if (Controller)
	{
		FVector2D inputVector = inputValue.Get<FVector2D>();

		const FRotator rotation = Controller->GetControlRotation();
		const FRotator yawRotation(0.f, rotation.Yaw, 0.f);

		const FVector forwardDirection = FRotationMatrix(yawRotation).GetUnitAxis(EAxis::X);
		const FVector rightDirection = FRotationMatrix(yawRotation).GetUnitAxis(EAxis::Y);

		AddMovementInput(forwardDirection, inputVector.Y);
		AddMovementInput(rightDirection, inputVector.X);

		if (cameraShakeComponent && attributes)
		{
			float multiplier = FMath::GetMappedRangeValueClamped(FVector2D(attributes->maxStamina, 0.f), FVector2D(1.f, 2.f), attributes->stamina);
			if (!bIsRunning)
				cameraShakeComponent->StartShake(EShakeType::Walking, multiplier);
			else
				cameraShakeComponent->StartShake(EShakeType::Running);
		}
	}
}

void ABaseFirstPersonCharacter::Sprint()
{
	if (attributes && attributes->HasEnoughStamina() && GetCharacterMovement()->CanEverJump() && GetCharacterMovement()->Velocity.Length() > 10.f)
	{
		ServerSprint();
	}
}

void ABaseFirstPersonCharacter::ServerSprint_Implementation()
{
	if (attributes && attributes->HasEnoughStamina() && GetCharacterMovement()->CanEverJump() && GetCharacterMovement()->Velocity.Size() > 10.f)
	{
		bIsRunning = true;
		GetCharacterMovement()->MaxWalkSpeed = attributes->runningSpeed * GetSpeedMultiplier();
		ClientSprint();

		StopCrouching();
		GetWorldTimerManager().SetTimer(drainStaminaHandle, this, &ABaseFirstPersonCharacter::DrainStamina, 0.01f, true);
		GetWorldTimerManager().ClearTimer(recoverStaminaHandle);
	}
}

void ABaseFirstPersonCharacter::ClientSprint_Implementation()
{
	GetCharacterMovement()->MaxWalkSpeed = attributes->runningSpeed * GetSpeedMultiplier();
}


void ABaseFirstPersonCharacter::StopSprinting()
{
	ServerStopSprinting();
}

void ABaseFirstPersonCharacter::ServerStopSprinting_Implementation()
{
	if (attributes && bIsRunning)
	{
		GetCharacterMovement()->MaxWalkSpeed = attributes->walkingSpeed * GetSpeedMultiplier();
		bIsRunning = false;
		ClientStopSprinting();

		GetWorldTimerManager().ClearTimer(drainStaminaHandle);
		GetWorldTimerManager().SetTimer(recoverStaminaHandle, this, &ABaseFirstPersonCharacter::RecoverStamina, 0.01f, true, 1.5f);
	}
}

void ABaseFirstPersonCharacter::ClientStopSprinting_Implementation()
{
	GetCharacterMovement()->MaxWalkSpeed = attributes->walkingSpeed * GetSpeedMultiplier();
}

void ABaseFirstPersonCharacter::Landed(const FHitResult& Hit)
{
	Super::Landed(Hit);

	//if (landingShake)
	//{
	//	float jumpHeight = abs(GetCharacterMovement()->Velocity.Z);
	//	APlayerCameraManager* cameraManager = UGameplayStatics::GetPlayerCameraManager(GetWorld(), 0);
	//	ULegacyCameraShake* shake = ULegacyCameraShake::StartLegacyCameraShake(cameraManager, landingShake);
	//	shake->OscillationDuration()
	//}

	if (cameraShakeComponent && attributes)
	{
		float jumpHeight = abs(GetCharacterMovement()->Velocity.Z);
		if (jumpHeight > attributes->hardLandThreshold)
			cameraShakeComponent->StartShake(EShakeType::HardLanding);
		else
			cameraShakeComponent->StartShake(EShakeType::Landing, 0.6f);
	}

	GetMesh()->SetRelativeLocation(FVector(0.f, 0.f, -89.f));
}

void ABaseFirstPersonCharacter::PerformJump()
{
	if (!GetCharacterMovement()->IsFalling() && IsPlayerActive())
	{
		StopCrouching();
		Jump();
		FVector newLocation = GetMesh()->GetRelativeLocation() - FVector(0.f, 0.f, 8.f);
		GetMesh()->SetRelativeLocation(newLocation);
		if (cameraShakeComponent)
			cameraShakeComponent->StartShake(EShakeType::Jumping);
	}
}

void ABaseFirstPersonCharacter::PerformCrouch()
{
	if (!bIsCrouching && IsPlayerActive() && !GetCharacterMovement()->IsFalling())
	{
		StopSprinting();
		bIsCrouching = true;
		FVector newLocation = GetMesh()->GetRelativeLocation() + FVector(-10.f, 0.f, 0.f);
		GetMesh()->SetRelativeLocation(newLocation);
		Crouch();
	}
}

void ABaseFirstPersonCharacter::StopCrouching()
{
	if (bIsCrouching)
	{
		bIsCrouching = false;
		FVector newLocation = GetMesh()->GetRelativeLocation() + FVector(10.f, 0.f, 0.f);
		GetMesh()->SetRelativeLocation(newLocation);
		UnCrouch();
	}
}

void ABaseFirstPersonCharacter::Look(const FInputActionValue& inputValue)
{
	if (Controller && !interactComponent->bIsInteracting)
	{
		FVector2D inputVector = inputValue.Get<FVector2D>();

		AddControllerYawInput(inputVector.X);
		AddControllerPitchInput(inputVector.Y);
	}
}

void ABaseFirstPersonCharacter::DrainStamina()
{
	if (attributes)
	{
		attributes->stamina = FMath::Clamp(attributes->stamina - attributes->staminaDrainRate, 0.f, attributes->maxStamina);
		attributes->OnRep_StaminaChanged();
		if (attributes->stamina == 0.f)
		{
			StopSprinting();
		}
	}
}

void ABaseFirstPersonCharacter::RecoverStamina()
{
	if (attributes)
	{
		float recoveryRate = attributes->staminaRecoveryRate;
		if (GetCharacterMovement()->Velocity.Length() <= 10.f)
			recoveryRate = recoveryRate * 2.f;
		attributes->stamina = FMath::Clamp(attributes->stamina + recoveryRate, 0.f, attributes->maxStamina);
		attributes->OnRep_StaminaChanged();
		if (attributes->stamina == attributes->maxStamina)
		{
			GetWorldTimerManager().ClearTimer(recoverStaminaHandle);
		}
	}
}

void ABaseFirstPersonCharacter::Interact()
{
	if (IsPlayerActive())
	{
		if (interactComponent && interactComponent->CanInteract())
		{
			interactComponent->StartInteraction();
		}
	}
}

void ABaseFirstPersonCharacter::StopInteracting()
{
	if (interactComponent)
	{
		interactComponent->StopInteraction();
	}
}

void ABaseFirstPersonCharacter::DropItem()
{
	if (interactComponent && interactComponent->IsHoldingItem())
	{
		StopItemAction();
		interactComponent->DropItem();
	}
}

void ABaseFirstPersonCharacter::TriggerItemAction()
{
	if (interactComponent && interactComponent->IsHoldingItem())
	{
		if(!interactComponent->heldItem->ActorHasTag("Body"))
			Server_TriggerItemAction();
	}
}

void ABaseFirstPersonCharacter::Server_TriggerItemAction_Implementation()
{
	if (interactComponent && interactComponent->IsHoldingItem())
	{
		interactComponent->ExecuteInteractEvent(interactComponent->heldItem);
	}
}

void ABaseFirstPersonCharacter::StopItemAction()
{
	if (interactComponent && interactComponent->IsHoldingItem())
	{
		Server_StopItemAction();
	}
}

void ABaseFirstPersonCharacter::Server_StopItemAction_Implementation()
{
	if (interactComponent && interactComponent->IsHoldingItem())
	{
		interactComponent->ExecuteInterruptEvent(interactComponent->heldItem);
	}
}

void ABaseFirstPersonCharacter::StartCameraFade(const bool fadeIn, const float duration)
{
	if (playerController)
	{
		APlayerCameraManager* cameraManager = playerController->PlayerCameraManager;

		if (fadeIn)
			cameraManager->StartCameraFade(1.f, 0.f, duration, FLinearColor::Black, true, false);
		else
			cameraManager->StartCameraFade(0.f, 1.f, duration, FLinearColor::Black, true, true);
	}
}

void ABaseFirstPersonCharacter::StopCameraFade()
{
	if (IsValid(playerController))
	{
		APlayerCameraManager* cameraManager = playerController->PlayerCameraManager;
		cameraManager->StopCameraFade();
	}
}

UOdinSynthComponent* ABaseFirstPersonCharacter::CreateVoiceSynthComponent_Implementation(const FVoiceRoomData& roomData, int64 peerID, UOdinPlaybackMedia* media)
{
	UOdinSynthComponent* synth = NewObject<UOdinSynthComponent>(this);
	synth->AttachToComponent(GetRootComponent(), FAttachmentTransformRules::SnapToTargetNotIncludingScale);
	synth->RegisterComponent();
	synth->AdjustAttenuation(roomData.attenuation->Attenuation);

	synth->Odin_AssignSynthToMedia(media);
	synth->Activate();

	OdinSynths.Add(synth);

	return synth;
}

bool ABaseFirstPersonCharacter::IsPlayerDead()
{
	return healthStatus == EActorHealthStatus::Dead;
}

bool ABaseFirstPersonCharacter::IsPlayerActive()
{
	return healthStatus == EActorHealthStatus::Awake;
}

bool ABaseFirstPersonCharacter::CanResurrect()
{
	return IsPlayerDead() && GetCharacterMovement() && GetMesh() && springArm && attributes;
}

void ABaseFirstPersonCharacter::DealDamage_Implementation(float damageAmount, bool bIsDamageOverTime, AActor* DamageCauser)
{
	if (IsValid(attributes) && !IsPlayerDead())
	{
		if (cameraShakeComponent)
		{
			if (!bIsDamageOverTime)
				cameraShakeComponent->StartShake(EShakeType::HardDamage);
			else
				cameraShakeComponent->StartShake(EShakeType::SoftDamage, 0.1f);
		}

		attributes->health = FMath::Clamp(attributes->health - damageAmount, 0.f, attributes->maxHealth);
		attributes->OnRep_HealthChanged();
		if (attributes->health <= 0.f)
		{
			PerformDeath();
		}
	}
}

EActorHealthStatus ABaseFirstPersonCharacter::GetHealthStatus_Implementation() const
{
	return healthStatus;
}

void ABaseFirstPersonCharacter::Kill_Implementation(AActor* DamageCauser)
{
	if (IsValid(attributes))
	{
		IDamageInterface::Execute_DealDamage(this, attributes->health, false, DamageCauser);
	}
}

void ABaseFirstPersonCharacter::Incapacitate_Implementation(AActor* sourceActor, float duration, float pushStrength)
{
	if (!IsPlayerDead())
	{
		Server_Incapacitate(sourceActor, duration, pushStrength);
	}
}

void ABaseFirstPersonCharacter::PerformDeath()
{
	if (!IsPlayerDead())
	{
		Server_Death();
	}
}

void ABaseFirstPersonCharacter::Server_Death_Implementation()
{
	if (!IsPlayerDead() && IsValid(springArm))
	{
		healthStatus = EActorHealthStatus::Dead;
		GetWorld()->GetTimerManager().ClearTimer(stunTimer);
		if (IsValid(interactComponent) && interactComponent->IsHoldingItem())
		{
			interactComponent->DropItem(false);
		}

		Multicast_Death();
	}
}

void ABaseFirstPersonCharacter::Multicast_Death_Implementation()
{
	Tags.Add("Body");
	StartRagdoll(GetVelocity());
	if (IsLocallyControlled())
	{
		ToggleUI(false);
		StartCameraFade(false);
	}

	GetWorld()->GetTimerManager().SetTimer(deathCameraTimer, this, &ABaseFirstPersonCharacter::DetachCameraFromBody, 3.f, false);
	OnDeathDelegate.Broadcast();
}

void ABaseFirstPersonCharacter::DetachCameraFromBody()
{
	FDetachmentTransformRules detachRules(
		EDetachmentRule::KeepWorld,
		EDetachmentRule::KeepWorld,
		EDetachmentRule::KeepWorld,
		true
	);
	springArm->DetachFromComponent(detachRules);
	FAttachmentTransformRules attachRules(
		EAttachmentRule::SnapToTarget,
		EAttachmentRule::KeepWorld,
		EAttachmentRule::KeepWorld,
		false
	);
	springArm->AttachToComponent(GetCapsuleComponent(), attachRules);
	springArm->TargetArmLength = 300.f;
	springArm->bUsePawnControlRotation = true;

	StartCameraFade(true, 1.f);

	if (IsLocallyControlled() && IsControlled())
	{
		GetMesh()->SetScalarParameterValueOnMaterials(FName("InputHigh"), 60.f);
		GetMesh()->SetScalarParameterValueOnMaterials(FName("InputLow"), 20.f);
	}
}


void ABaseFirstPersonCharacter::Server_UpdateCapsule_Implementation()
{
	if (IsPlayerDead())
	{
		FVector_NetQuantize ragdollLocation = GetMesh()->GetSocketLocation(FName("pelvis"));
		Multicast_UpdateCapsule(ragdollLocation);
	}
}

void ABaseFirstPersonCharacter::Multicast_UpdateCapsule_Implementation(FVector_NetQuantize ragdollLocation)
{
	ragdollLocation = GetMesh()->GetSocketLocation(FName("pelvis"));
	GetCapsuleComponent()->SetWorldLocation(ragdollLocation);
}

void ABaseFirstPersonCharacter::Server_Incapacitate_Implementation(AActor* sourceActor, float duration, float pushStrength)
{
	if (IsPlayerActive())
	{
		healthStatus = EActorHealthStatus::Incapacitated;
		if (IsValid(interactComponent) && interactComponent->IsHoldingItem())
		{
			interactComponent->DropItem(false);
		}

		Multicast_Incapacitate(sourceActor, duration, pushStrength);
	}

	FMath::IsNearlyZero(duration) 
		? GetWorld()->GetTimerManager().ClearTimer(stunTimer) 
		: GetWorld()->GetTimerManager().SetTimer(stunTimer, this, &ABaseFirstPersonCharacter::WakePlayerUp, duration, false);
}

void ABaseFirstPersonCharacter::Multicast_Incapacitate_Implementation(AActor* sourceActor, float duration, float pushStrength)
{	
	FVector_NetQuantize direction = (GetActorLocation() - sourceActor->GetActorLocation()).GetSafeNormal();
	StartRagdoll(direction * pushStrength);

	if (IsLocallyControlled())
	{
		ToggleUI(false);
		//StartCameraFade(false);
	}
}

void ABaseFirstPersonCharacter::WakePlayerUp()
{
	if (!IsPlayerDead())
	{
		Server_WakeUp();
	}
}

void ABaseFirstPersonCharacter::Server_WakeUp_Implementation()
{
	if (!IsPlayerDead())
	{
		healthStatus = EActorHealthStatus::Awake;
		GetWorld()->GetTimerManager().ClearTimer(stunTimer);
		Multicast_WakeUp();
	}
}

void ABaseFirstPersonCharacter::Multicast_WakeUp_Implementation()
{
	StopRagdoll();
	if (IsLocallyControlled())
	{
		ToggleUI(true);
	}
}

void ABaseFirstPersonCharacter::Resurrect_Implementation(AActor* Healer, float healingAmount)
{
	if (CanResurrect())
	{
		Server_Resurrect(healingAmount);
	}
}

void ABaseFirstPersonCharacter::Server_Resurrect_Implementation(float healingAmount)
{
	if (CanResurrect())
	{
		attributes->health = healingAmount;
		attributes->OnRep_HealthChanged();
		healthStatus = EActorHealthStatus::Awake;
		Tags.Empty();

		Multicast_Resurrect(springArmOffset, healingAmount);
	}
}

void ABaseFirstPersonCharacter::Multicast_Resurrect_Implementation(FTransform cameraTransform, float healingAmount)
{
	StopRagdoll();
	if (IsLocallyControlled() && IsControlled())
	{
		ToggleUI(true);
		StopCameraFade();

		GetMesh()->SetScalarParameterValueOnMaterials(FName("InputHigh"), 15.f);
		GetMesh()->SetScalarParameterValueOnMaterials(FName("InputLow"), 10.f);
	}

	GetWorld()->GetTimerManager().ClearTimer(deathCameraTimer);
	OnReviveDelegate.Broadcast();
}

void ABaseFirstPersonCharacter::ToggleUI(bool bShowUI)
{
	if (playerController)
	{
		if (AStormEscapeHUD* hudREF = Cast<AStormEscapeHUD>(playerController->GetHUD()))
		{
			hudREF->ToggleUI(bShowUI);
		}
	}
}

void ABaseFirstPersonCharacter::StartRagdoll(FVector_NetQuantize impulse)
{
	if (!bIsRagdolling)
	{
		bIsRagdolling = true;
		if (IsLocallyControlled())
		{
			StopSprinting();
			if (IsValid(cameraShakeComponent))
			{
				cameraShakeComponent->EnableAllShakes(false);
			}
		}

		meshOffset = FTransform(GetMesh()->GetRelativeRotation(), GetMesh()->GetRelativeLocation(), FVector(1.f, 1.f, 1.f));
		GetCharacterMovement()->SetMovementMode(EMovementMode::MOVE_None);
		GetCapsuleComponent()->SetCollisionEnabled(ECollisionEnabled::NoCollision);
		GetMesh()->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

		FDetachmentTransformRules detachRules(
			EDetachmentRule::KeepWorld,
			EDetachmentRule::KeepWorld,
			EDetachmentRule::KeepWorld,
			true
		);

		springArmOffset = FTransform(springArm->GetRelativeRotation(), springArm->GetRelativeLocation(), FVector(1.f, 1.f, 1.f));
		springArm->AddRelativeLocation(FVector(5.f, -5.f, 0.f));
		springArm->bUsePawnControlRotation = false;
		GetMesh()->DetachFromComponent(detachRules);
		GetMesh()->SetAllBodiesBelowSimulatePhysics(FName("pelvis"), true);
	}

	if (!impulse.IsNearlyZero())
	{
		GetMesh()->AddImpulseAtLocation(impulse, GetActorLocation(), FName("spine_04"));
	}
}

void ABaseFirstPersonCharacter::StopRagdoll()
{
	if (bIsRagdolling)
	{
		bIsRagdolling = false;
		if (IsLocallyControlled() && IsValid(cameraShakeComponent))
		{
			cameraShakeComponent->EnableAllShakes(true);
		}

		GetCharacterMovement()->SetMovementMode(EMovementMode::MOVE_Walking);
		GetCapsuleComponent()->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);
		GetMesh()->SetAllBodiesBelowSimulatePhysics(FName("pelvis"), false);

		FAttachmentTransformRules attachRules(
			EAttachmentRule::SnapToTarget,
			EAttachmentRule::SnapToTarget,
			EAttachmentRule::KeepWorld,
			false
		);

		SetActorLocation(GetMesh()->GetBoneLocation(FName("pelvis")));
		GetMesh()->AttachToComponent(GetCapsuleComponent(), attachRules);
		GetMesh()->SetRelativeTransform(meshOffset);

		FDetachmentTransformRules detachRules(
			EDetachmentRule::KeepWorld,
			EDetachmentRule::KeepWorld,
			EDetachmentRule::KeepWorld,
			true
		);
		springArm->DetachFromComponent(detachRules);
		springArm->TargetArmLength = 0.f;
		springArm->bUsePawnControlRotation = true;
		springArm->AttachToComponent(GetMesh(), attachRules, FName("head"));
		springArm->SetRelativeTransform(springArmOffset);
	}
}

void ABaseFirstPersonCharacter::PushPlayerOffCollision(TArray<AActor*> ovelappingActors, float DeltaTime)
{
	FVector_NetQuantize pushVector = FVector_NetQuantize::Zero();
	float closestPlayerDistance = 100.f;
	for (AActor* player : ovelappingActors)
	{
		FVector_NetQuantize vectorToPlayer = GetActorLocation() - player->GetActorLocation();
		vectorToPlayer.Normalize();
		pushVector += vectorToPlayer;
		float playerDistance = GetDistanceTo(player);
		if (playerDistance < closestPlayerDistance)
		{
			closestPlayerDistance = playerDistance;
		}
	}
	float pushStrenth = FMath::GetMappedRangeValueClamped(FVector2D(10.f, 70.f), FVector2D(2.f, 0.5f), closestPlayerDistance);
	pushStrenth = pushStrenth * DeltaTime * 45.f;
	FVector_NetQuantize pushDirection = pushVector * pushStrenth;
	SetActorLocation(GetActorLocation() + pushDirection, true);
}
﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Data/PDA_GameplayMaps.h"
#include "GameFramework/GameStateBase.h"
#include "LobbyGameState.generated.h"

USTRUCT(BlueprintType)
struct FCrewMember
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly)
	FString PlayerName;

	// Add more fields as needed (avatar, team, color, etc.)
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPlayerListChangedSignature, const TArray<FCrewMember>&, PlayerList);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnMapChanged, const FPlayMapInfo&, MapInfo);

/**
 * 
 */
UCLASS()
class STORMESCAPE_API ALobbyGameState : public AGameStateBase
{
	GENERATED_BODY()

public:

	UPROPERTY(ReplicatedUsing = OnRep_PlayerInfoList, BlueprintReadOnly)
	TArray<FCrewMember> PlayerInfoList;

	UPROPERTY(ReplicatedUsing = OnRep_CurrentPlayMap, BlueprintReadOnly)
	FPlayMapInfo CurrentPlayMap;

	UFUNCTION(BlueprintCallable)
	void SetPlayMapInfo(const FPlayMapInfo& MapInfo);

	void AddPlayerInfo(const FCrewMember& Info);
	void RemovePlayerInfoByName(const FString& PlayerName);

	/** Delegate broadcast when player list is updated */
	UPROPERTY(BlueprintAssignable)
	FOnPlayerListChangedSignature OnPlayerListChanged;
	
	UPROPERTY(BlueprintAssignable)
	FOnMapChanged OnMapChanged;
protected:

	UFUNCTION()
	void OnRep_CurrentPlayMap();
	
	UFUNCTION()
	void OnRep_PlayerInfoList();
	
	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

};

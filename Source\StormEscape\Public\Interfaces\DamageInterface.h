#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "DamageInterface.generated.h"

UENUM(BlueprintType)
enum class EActorHealthStatus : uint8
{
	None = 0			UMETA(DisplayName = "None"),
	Dead = 1			UMETA(DisplayName = "Dead"),
	Incapacitated = 2	UMETA(DisplayName = "Incapacitated"),
	Awake = 3			UMETA(DisplayName = "Awake")
};

UINTERFACE(MinimalAPI)
class UDamageInterface : public UInterface
{
	GENERATED_BODY()
};

class STORMESCAPE_API IDamageInterface
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void DealDamage(float damageAmount, bool bIsDamageOverTime, AActor* DamageCauser);

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	EActorHealthStatus GetHealthStatus() const;

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void Kill(AActor* DamageCauser);

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void Resurrect(AActor* Healer, float healingAmount);

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void Incapacitate(AActor* SourceActor, float duration = 0.f, float pushStrength = 0.f);
};

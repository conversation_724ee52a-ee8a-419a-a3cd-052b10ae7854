#include "Van/FuelCap.h"
#include "Van/GameplayVan.h"
#include "Components/SphereComponent.h"
#include "Net/UnrealNetwork.h"
#include "CableComponent.h"
#include "PhysicsEngine/PhysicsConstraintComponent.h"

AFuelCap::AFuelCap()
{
	cableComponent = CreateDefaultSubobject<UCableComponent>("Cable");
	cableComponent->SetupAttachment(capMesh);

	physicsConstraint = CreateDefaultSubobject<UPhysicsConstraintComponent>("Constraint");
	physicsConstraint->SetupAttachment(itemMesh);
	physicsConstraint->SetConstrainedComponents(itemMesh, FName("None"), capMesh, FName("None"));

	vehiclePart = EVehiclePart::Fuel;
}

void AFuelCap::Multicast_Open_Implementation()
{
	capMesh->SetSimulatePhysics(true);
}

void AFuelCap::Multicast_Close_Implementation()
{
	capMesh->SetSimulatePhysics(false);

	FAttachmentTransformRules attachRules(
		EAttachmentRule::SnapToTarget,
		EAttachmentRule::SnapToTarget,
		EAttachmentRule::KeepWorld,
		false
	);

	capMesh->AttachToComponent(itemMesh, attachRules);
}

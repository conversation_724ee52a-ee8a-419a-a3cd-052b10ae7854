#include "Van/Fuses.h"
#include "Van/FuseSpot.h"

AFuses::AFuses()
{
	bIsPickable = true;
	bRequiresToolBox = false;
	bIsRemovable = true;
	vehiclePart = EVehiclePart::Fuses;

	timeToRemove = 6.f;
	timeToInstall = 4.f;
	timeToInsert = 4.f;
}

void AFuses::SwapVehicleParts(UPrimitiveComponent* InVehiclePartSpot)
{
	if (vehiclePartSpot)
	{
		if (AFuseSpot* fuseBox = Cast<AFuseSpot>(vehiclePartSpot))
		{
			Super::SwapVehicleParts(fuseBox->GetFuseAttachment());
		}
	}
}

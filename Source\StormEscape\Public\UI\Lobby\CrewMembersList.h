﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "Blueprint/UserWidget.h"
#include "CrewMembersList.generated.h"

struct FCrewMember;
/**
 * 
 */
UCLASS()
class STORMESCAPE_API UCrewMembersList : public UCommonActivatableWidget
{
	GENERATED_BODY()

public:

	virtual void NativeConstruct() override;

	UFUNCTION(BlueprintImplementableEvent)
	void UpdatePlayerList(const TArray<FCrewMember>& Players);
};

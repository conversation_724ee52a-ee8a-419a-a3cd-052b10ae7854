// Fill out your copyright notice in the Description page of Project Settings.


#include "Weather/Tornado.h"
#include "BaseFirstPersonCharacter.h"
#include "Kismet/GameplayStatics.h"
#include "Weather/WeatherImportantTypes.h"
#include "StormEscapePlayerController.h"

// Sets default values
ATornado::ATornado()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	RootComponent = CreateDefaultSubobject<USceneComponent>("RootComponent");

	CapsuleComponent = CreateDefaultSubobject<UCapsuleComponent>("HitCapsule");
	CapsuleComponent->InitCapsuleSize(500.0f, 1000.0f);
	CapsuleComponent->SetCollisionProfileName(UCollisionProfile::DefaultProjectile_ProfileName);
	CapsuleComponent->CanCharacterStepUpOn = ECB_No;
	CapsuleComponent->SetupAttachment(RootComponent);
}

void ATornado::PostInitializeComponents()
{
	Super::PostInitializeComponents();

	// TO DO: find a better way to detect hits, probably using distances.
	OnActorBeginOverlap.AddUniqueDynamic(this, &ATornado::OnActorCollision);
}

void ATornado::Tick(float DeltaTime)
{
	if (HasAuthority() && bIsMoving)
	{
		SetActorLocation(GetActorLocation() + speed * direction * DeltaTime, true);
	}
}

float ATornado::GetSpawnDistance() const
{
	float distance = FMath::RandRange(minDistance, maxDistance) * FDistanceUnits::KmToCm();
	return CapsuleComponent->GetScaledCapsuleRadius() + distance;
}

void ATornado::StartMovement(FVector destiny)
{
	FVector journey = destiny - GetActorLocation();
	direction = journey.GetSafeNormal();
	speed = (journey.Length() - CapsuleComponent->GetScaledCapsuleRadius()) / (arrivalTime * FTimeUnits::MinToS());

	bIsMoving = true;
}

void ATornado::OnActorCollision(AActor* SelfActor, AActor* OtherActor)
{
	if (HasAuthority())
	{
		ABaseFirstPersonCharacter* character = Cast<ABaseFirstPersonCharacter>(OtherActor);
		AStormEscapePlayerController* playerController = character ? Cast<AStormEscapePlayerController>(character->GetController()) : nullptr;

		if (IsValid(playerController))
		{
			playerController->NotifyTornadoHit(this);
		}
	}
}


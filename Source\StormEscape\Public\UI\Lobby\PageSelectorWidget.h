﻿#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "Runtime/UMG/Public/Blueprint/UserWidget.h"
#include "PageSelectorWidget.generated.h"

USTRUCT(BlueprintType)
struct FPaginationEntry
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite)
	int32 PageNumber;

	UPROPERTY(BlueprintReadWrite)
	bool bIsEllipsis;

	FPaginationEntry()
		: PageNumber(0), bIsEllipsis(true) {}

	FPaginationEntry(int32 InPageNumber)
		: PageNumber(InPageNumber), bIsEllipsis(false) {}
};

/**
 * Widget for handling pagination logic
 */
UCLASS()
class STORMESCAPE_API UPageSelectorWidget : public UCommonActivatableWidget
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, Category = "Pagination")
	TArray<FPaginationEntry> GeneratePagination(int32 CurrentPage, int32 TotalPages, int32 VisibleRange = 2);
};
// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Items/BaseDeviceItem.h"
#include "CableComponent.h"
#include "PhysicsEngine/PhysicsConstraintComponent.h"
#include "Components/ArrowComponent.h"
#include "BaseRemoteItem.generated.h"

/**
 * 
 */

UCLASS()
class STORMESCAPE_API ABaseRemoteItem : public ABaseDeviceItem
{
	GENERATED_BODY()
	
public:
	ABaseRemoteItem();

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	void NotifyPickUp_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	void NotifyDrop_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	void SetTarget(ABaseDeviceItem* target, float maxDistance, UCableComponent* cable = nullptr, UPhysicsConstraintComponent* constraint = nullptr, float pull = 0.f);

	void ToggleRemote(bool bEnabled);

protected:
	bool CanActivate_Implementation() const override { return bRemoteEnabled && Super::CanActivate_Implementation(); }

	UFUNCTION(Client, Reliable)
	void Client_AddInputToTarget(AActor* interactor);

	UFUNCTION(Client, Reliable)
	void Client_RemoveInputFromTarget(AActor* interactor);

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UArrowComponent* cableSocket;

	UFUNCTION()
	virtual void OnRep_TargetItemUpdate();

	UPROPERTY(ReplicatedUsing=OnRep_TargetItemUpdate, EditInstanceOnly, BlueprintReadWrite)
	ABaseDeviceItem* targetItem;

	UPROPERTY(EditInstanceOnly, BlueprintReadWrite)
	float range;

	UPROPERTY(BlueprintReadWrite)
	bool bRemoteEnabled;

	UInteractComponent* ownerInteractComponent;

	UPROPERTY(BlueprintReadOnly)
	UPhysicsConstraintComponent* targetConstraint;

private:
	UFUNCTION()
	void CheckCableLimit();

	FTimerHandle cableTimer;

	float rangeSquared;

	float pullStrength;

	FVector targetAttachPoint;

/* Blueprint Events */
protected:
	UFUNCTION(BlueprintImplementableEvent, DisplayName="OnTargetChanged")
	void K2_OnTargetChanged(ABaseDeviceItem* target);
};

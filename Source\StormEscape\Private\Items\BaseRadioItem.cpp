// Fill out your copyright notice in the Description page of Project Settings.


#include "Items/BaseRadioItem.h"
#include "Net/UnrealNetwork.h"

ABaseRadioItem::ABaseRadioItem()
{
	activationType = EActivationType::Toggle;
	tailCooldown = 0.5f;

	synthClass = UOdinSynthComponent::StaticClass();
	loopbackClass = UOdinAudioGeneratorLoopbackComponent::StaticClass();
}

void ABaseRadioItem::BeginPlay()
{
	Super::BeginPlay();

	transmittingID = INDEX_NONE;
	receivingID = INDEX_NONE;
	bOnTailCooldown = false;

	radioSystem = GetWorld()->GetSubsystem<URadioSubsystem>();
	if (radioSystem)
	{
		radioSystem->RegisterReceiver(this);
		radioSystem->onTransmittersChangedDelegate.AddUniqueDynamic(this, &ABaseRadioItem::OnSignalChanged);
	}
}

void ABaseRadioItem::BindCustomInputs(UEnhancedInputComponent* inputComponent)
{
	Super::BindCustomInputs(inputComponent);

	if (inputComponent && microphoneAction)
	{
		customBindings.Add(inputComponent->BindAction(microphoneAction, ETriggerEvent::Started, this, &ABaseRadioItem::OpenMicrophone));
		customBindings.Add(inputComponent->BindAction(microphoneAction, ETriggerEvent::Completed, this, &ABaseRadioItem::CloseMicrophone));
	}
}

void ABaseRadioItem::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
	if (radioSystem)
	{
		radioSystem->UnregisterReceiver(this);
		radioSystem->onTransmittersChangedDelegate.RemoveAll(this);
	}

	Super::EndPlay(EndPlayReason);
}

void ABaseRadioItem::Activate_Implementation()
{
	Super::Activate_Implementation();
	bIsMicOpen ? Talk() : Listen();
}

void ABaseRadioItem::Deactivate_Implementation()
{
	bIsMicOpen ? StopTalking() : StopListening();
	Super::Deactivate_Implementation();
}

void ABaseRadioItem::Talk()
{
	if (!bOnTailCooldown && transmittingID == INDEX_NONE)
	{
		Server_Talk();
	}
}

void ABaseRadioItem::StopTalking()
{
	if (!bOnTailCooldown && transmittingID != INDEX_NONE)
	{
		Server_StopTalking();
	}
}

void ABaseRadioItem::SetTransmittingID(int64 peerID)
{
	if (transmittingID != peerID)
	{
		int64 oldID = transmittingID;
		transmittingID = peerID;
		OnRep_TransmittingIdUpdate(oldID);
	}
}

void ABaseRadioItem::Server_Talk_Implementation()
{
	if (radioSystem)
	{
		SetTransmittingID(radioSystem->GetPeerFromSpeaker(GetOwner()));
		radioSystem->NotifyTransmissionStart(transmittingID);
	}
}

void ABaseRadioItem::Server_StopTalking_Implementation()
{
	if (radioSystem)
	{
		radioSystem->NotifyTransmissionEnd(transmittingID);
		SetTransmittingID(INDEX_NONE);
	}
}

void ABaseRadioItem::SetReceivingID(int64 peerID)
{
	if (receivingID != peerID)
	{
		int64 oldID = receivingID;
		receivingID = peerID;
		OnRep_ReceivingIdUpdate(oldID);
	}
}

void ABaseRadioItem::Server_Listen_Implementation()
{
	if (radioSystem)
	{
		SetReceivingID(radioSystem->GetTransmitterID());
	}
}

void ABaseRadioItem::Server_StopListening_Implementation()
{
	if (radioSystem)
	{
		SetReceivingID(INDEX_NONE);
	}
}

void ABaseRadioItem::OnRep_ReceivingIdUpdate(int64 oldID)
{
	if (receivingID != INDEX_NONE)
	{
		ToggleSynth(receivingID, true);
		K2_OnSignalStarted();
		OnStartedReceivingDelegate.Broadcast();
	}
	else
	{
		ToggleSynth(oldID, false);
		K2_OnSignalEnded();
		OnStoppedReceivingDelegate.Broadcast();
	}
}

void ABaseRadioItem::StartTailCooldown()
{
	if (!bOnTailCooldown)
	{
		tailCooldownTime = tailCooldown;
		bOnTailCooldown = true;
		FTimerDelegate cooldownDelegate = FTimerDelegate::CreateUObject(this, &ABaseRadioItem::UpdateTailCooldown, updateFreq);
		GetWorld()->GetTimerManager().SetTimer(cooldownTimer, cooldownDelegate, updateFreq, true);
	}
}

void ABaseRadioItem::UpdateTailCooldown(float deltaSeconds)
{
	tailCooldownTime -= deltaSeconds;
	if (tailCooldownTime <= 0.f)
	{
		StopTailCooldown();
	}
}

void ABaseRadioItem::StopTailCooldown()
{
	if (bOnTailCooldown)
	{
		tailCooldownTime = 0.f;
		bOnTailCooldown = false;
		GetWorld()->GetTimerManager().ClearTimer(cooldownTimer);
		bIsMicOpen ? Talk() : Listen();
	}
}

void ABaseRadioItem::ToggleSynth(int64 peerID, bool bOn)
{
	if (peerID != INDEX_NONE && peerToSynthMap.Contains(peerID))
	{
		bOn ? peerToSynthMap[peerID]->Activate() : peerToSynthMap[peerID]->Deactivate();
	}
}

void ABaseRadioItem::OnRep_TransmittingIdUpdate(int64 oldID)
{
	K2_OnSpeakerChanged(transmittingID != INDEX_NONE, transmittingID);
	
	if (transmittingID != INDEX_NONE)
	{
		K2_OnTransmissionStarted();
		OnStartedTransmittingDelegate.Broadcast();
		if (transmittingID == localPeerID && localCapture)
		{
			localCapture->SetIsMuted(false);
		}
	}
	else
	{
		K2_OnTransmissionEnded();
		OnStoppedTransmittingDelegate.Broadcast();
		if (oldID == localPeerID && localCapture)
		{
			localCapture->SetIsMuted(true);
		}
	}
}

void ABaseRadioItem::OnSignalChanged(int64 peerID)
{
	if (bActivated && !bOnTailCooldown && transmittingID == INDEX_NONE)
	{
		if (receivingID == INDEX_NONE)
		{
			SetReceivingID(peerID);
		}
		else
		{
			SetReceivingID(INDEX_NONE);
			StartTailCooldown();
		}
	}
}

void ABaseRadioItem::Listen()
{
	if (!bOnTailCooldown && receivingID == INDEX_NONE)
	{
		Server_Listen();
	}
}

void ABaseRadioItem::StopListening()
{
	if (!bOnTailCooldown && receivingID != INDEX_NONE)
	{
		Server_StopListening();
	}
}

void ABaseRadioItem::OpenMicrophone()
{
	bIsMicOpen = true;
	if (bActivated)
	{
		StopListening();
		Talk();
	}
}

void ABaseRadioItem::CloseMicrophone()
{
	bIsMicOpen = false;
	if (bActivated)
	{
		StopTalking();
		StartTailCooldown();
	}
}

UOdinSynthComponent* ABaseRadioItem::CreateVoiceSynthComponent_Implementation(const FVoiceRoomData& roomData, int64 peerID, UOdinPlaybackMedia* media)
{
	UOdinSynthComponent* synth = Cast<UOdinSynthComponent>(AddComponentByClass(synthClass, true, FTransform(), false));
	synth->AttachToComponent(GetRootComponent(), FAttachmentTransformRules::SnapToTargetNotIncludingScale);
	synth->RegisterComponent();
	synth->AdjustAttenuation(roomData.attenuation->Attenuation);

	synth->Odin_AssignSynthToMedia(media);
	synth->Deactivate();
	peerToSynthMap.FindOrAdd(peerID) = synth;

	return synth;
}

UOdinAudioGeneratorLoopbackComponent* ABaseRadioItem::CreateVoiceLoopbackComponent_Implementation(const FVoiceRoomData& roomData, int64 peerID)
{
	UOdinAudioGeneratorLoopbackComponent* loopback = Cast<UOdinAudioGeneratorLoopbackComponent>(AddComponentByClass(loopbackClass, true, FTransform(), false));
	loopback->AttachToComponent(GetRootComponent(), FAttachmentTransformRules::SnapToTargetNotIncludingScale);
	loopback->RegisterComponent();
	loopback->Deactivate();

	loopback->bAllowSpatialization = true;
	loopback->bOverrideAttenuation = true;
	loopback->AttenuationOverrides = roomData.attenuation->Attenuation;
	auto AudioComponentPointer = loopback->GetAudioComponent();
	if (AudioComponentPointer) {
		AudioComponentPointer->AdjustAttenuation(roomData.attenuation->Attenuation);
	}

	loopback->SetLoopbackGenerator(roomData.audioCapture);
	loopback->Deactivate();
	peerToSynthMap.FindOrAdd(peerID) = loopback;
	localPeerID = peerID;
	localCapture = roomData.audioCapture;

	return loopback;
}

void ABaseRadioItem::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(ABaseRadioItem, transmittingID);
	DOREPLIFETIME(ABaseRadioItem, receivingID);
	DOREPLIFETIME(ABaseRadioItem, bOnTailCooldown);
	DOREPLIFETIME(ABaseRadioItem, transmittingID);
}

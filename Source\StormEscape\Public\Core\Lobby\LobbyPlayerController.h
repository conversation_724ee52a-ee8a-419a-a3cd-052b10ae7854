﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerController.h"
#include "Interfaces/ControllerInterface.h"
#include "LobbyPlayerController.generated.h"

class UGameplayVoiceChatComponent;
/**
 * 
 */
UCLASS()
class STORMESCAPE_API ALobbyPlayerController : public APlayerController , public IControllerInterface
{
	GENERATED_BODY()


protected:
	
	virtual void BeginPlay() override;

	void KickToMainMenu_Implementation(const FString& Reason) override;
	
	UFUNCTION(Client, Reliable)
	void Client_KickToMainMenu(const FString& Reason);

};

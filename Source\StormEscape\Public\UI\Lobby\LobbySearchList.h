﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"

#include "LobbySearchList.generated.h"

struct FLobbySearchResult;
class ULobbyResultContainer;
/**
 * 
 */
UCLASS()
class STORMESCAPE_API ULobbySearchList : public UCommonActivatableWidget
{
	GENERATED_BODY()

public:

	UFUNCTION(BlueprintImplementableEvent)
	void PopulateList(const TArray<FLobbySearchResult>& Results);
};

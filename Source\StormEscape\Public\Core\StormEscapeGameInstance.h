// Fill out your copyright notice in the Description page of Project Settings.
#pragma once

#include "CoreMinimal.h"
#include "AdvancedFriendsGameInstance.h"
#include "UI/LoadingScreenWidget.h"
#include "StormEscapeGameInstance.generated.h"

USTRUCT(BlueprintType)
struct FLobbyDisplayInfo
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly)
	FString LobbyName = "Unknown";

	UPROPERTY(BlueprintReadOnly)
	int32 MaxPlayers = 4;

	UPROPERTY(BlueprintReadOnly)
	int32 CurrentPlayers = 1;

	//Cheat Code: 0 = EN , 1 = ES , 2 = FR
	UPROPERTY(BlueprintReadOnly)
	int32 PreferredLanguage = 0;
};

USTRUCT(BlueprintType)
struct FLobbySearchResult
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly)
	FLobbyDisplayInfo LobbyInfo = {""};
	
	UPROPERTY(BlueprintReadOnly)
	int32 Ping = 2025;
	
	FOnlineSessionSearchResult SessionResult = {};
	int32 ServerIndex = 0;
};

USTRUCT(BlueprintType)
struct FCreateLobby
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite)
	FString LobbyName = "Unknown";

	UPROPERTY(BlueprintReadWrite)
	int32 MaxPlayers = 4;
	
	UPROPERTY(BlueprintReadWrite)
	int32 Difficulty = 0;

	UPROPERTY(BlueprintReadWrite)
	bool bIsPrivateRoom = false;
	
	//Cheat Code: 0 = EN , 1 = ES , 2 = FR
	UPROPERTY(BlueprintReadWrite)
	int32 PreferredLanguage = 0;
	
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnStormSessionCreated, bool, bSucceeded);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnStormSessionJoined, bool, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnStormSessionsFound, const TArray<FLobbySearchResult>&, Lobbies);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnStormSessionsSelected, FLobbySearchResult, Lobbies, UUserWidget* ,Widget);


UCLASS()
class STORMESCAPE_API UStormEscapeGameInstance : public UAdvancedFriendsGameInstance
{
	GENERATED_BODY()

public:
	
	UFUNCTION(BlueprintCallable, meta = (WorldContext = "WorldContextObject"))
	static UStormEscapeGameInstance* GetActive_GameInstance(UObject* WorldContextObject);

	/** External calling */
	UFUNCTION(BlueprintCallable)
	void HostLobbySession(const FCreateLobby& LobbyData);

	UFUNCTION(BlueprintCallable)
	void SearchForLobbies();

	UFUNCTION(BlueprintCallable)
	void JoinLobby(const FLobbySearchResult& LobbyData);

	UFUNCTION(BlueprintCallable)
	void SetSelectedLobby(const FLobbySearchResult& LobbyData, UUserWidget* Widget);

	UFUNCTION(BlueprintCallable)
	void LeaveLobby();
	
	UFUNCTION(BlueprintCallable)
	FLobbyDisplayInfo GetCurrentLobbyData();

	UFUNCTION(BlueprintCallable)
	void KickAndReturnToMenu(FString Reason);
	
	UFUNCTION(BlueprintCallable)
	void DestroyExistingSessionIfAny();

	UFUNCTION(BlueprintCallable)
	void BeginLoadingScreen(const FString& String);

	UFUNCTION(BlueprintCallable)
	FORCEINLINE FLobbySearchResult GetSelectedLobby() const { return SelectedLobby; }

	/** Blueprint delegates mainly used for UI */
	UPROPERTY(BlueprintAssignable, Category = "Sessions")
	FOnStormSessionCreated OnStormSessionCreated;

	UPROPERTY(BlueprintAssignable, Category = "Sessions")
	FOnStormSessionsFound OnStormSessionsFound;

	UPROPERTY(BlueprintAssignable, Category = "Sessions")
	FOnStormSessionsSelected OnStormSessionSelected;

	UPROPERTY(BlueprintAssignable, Category = "Sessions")
	FOnStormSessionJoined OnStormSessionJoined;
	
	UPROPERTY(BlueprintReadWrite)
	bool bWasKicked;

	UPROPERTY(BlueprintReadWrite)
	FString KickReason;

	UPROPERTY(BlueprintReadOnly)
	int32 GameDifficulty;
	
	UPROPERTY(BlueprintReadOnly)
	bool bPrivateGame;

	//This is used to give time for the loading screen to play
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	float HostTravelDelay = 3.0f;

	/** Voice Chat Related */
	
	UFUNCTION(BlueprintCallable)
	ACharacter* FindCharacterByGuid(FGuid NewGUID);

	UFUNCTION(BlueprintCallable)
	void RegisterCharacterToGuid(FGuid GUID,ACharacter* Character);

	UPROPERTY(BlueprintReadOnly)
	bool bFirstLaunch = true;
	
protected:
	
	void EndLoadingScreen(UWorld* World);
	virtual void Init() override;

private:

	void ThrowErrorPopup(FString head,FString body);

	TMap<FGuid, ACharacter*> GuidToCharacterMap;
	TSharedPtr<FOnlineSessionSearch> SessionSearch;
	IOnlineSessionPtr SessionInterface;

	void OnCreateSessionComplete(FName SessionName, bool bWasSuccessful);
	void OnFindSessionsComplete(bool bWasSuccessful);
	void OnJoinSessionComplete(FName SessionName, EOnJoinSessionCompleteResult::Type Result);
	void OnSessionDestroyed(FName Name, bool bArg);
	void HandleNetworkFailure(UWorld* World, UNetDriver* NetDriver, ENetworkFailure::Type ErrorType, const FString& ErrorMessage);

	ULoadingScreenWidget* loadingScreen;
	ULoadingScreenWidget* HostloadingScreen;
	FLobbySearchResult SelectedLobby;

};
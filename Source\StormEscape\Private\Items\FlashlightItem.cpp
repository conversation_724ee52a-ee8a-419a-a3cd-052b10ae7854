// Fill out your copyright notice in the Description page of Project Settings.


#include "Items/FlashlightItem.h"

AFlashlightItem::AFlashlightItem()
{
	bIsPickable = true;
	activationType = EActivationType::Toggle;
	progressType = EProgressType::None;

	light = CreateDefaultSubobject<USpotLightComponent>("Light");
	light->SetupAttachment(RootComponent);

	bleed = CreateDefaultSubobject<USpotLightComponent>("Bleed");
	bleed->SetupAttachment(RootComponent);

	color = FColor::White;
	intensity = 15000.f;
	reach = 1000.f;
	coneAngle = { 12.f, 30.f };
	bleedPercent = 0.05f;
	bleedAngle = { 50.f, 70.f };

	flickerStrength = .1f;
	flickerChance = .3f;
	flickerCooldown = 5.f;
	flickerDuration = { .3f, 1.f };
	flickerSpeed = 100.f;

	intensityLoss = 1.f;
	reachLoss = 1.f;
	angleLoss = 1.f;

	blackoutDuration = { .3f, 5.f };
	blackoutChance = .1f;

	UpdateLights();
}

void AFlashlightItem::BeginPlay()
{
	Super::BeginPlay();
	UpdateLights();

	light->SetVisibility(false);
	bleed->SetVisibility(false);
}

void AFlashlightItem::UpdateLights(float loss)
{
	float intensityPercent = 1.f - loss * intensityLoss;
	float reachPercent = 1.f - loss * reachLoss;
	float conePercent = 1.f - loss * angleLoss;

	light->SetLightFColor(color);
	light->SetIntensity(intensity * (1.f - bleedPercent) * GetIntensityMultiplier() * intensityPercent);
	light->SetAttenuationRadius(reach * reachPercent);
	light->SetInnerConeAngle(coneAngle.X * conePercent);
	light->SetOuterConeAngle(coneAngle.Y * conePercent);

	bleed->SetLightFColor(color);
	bleed->SetIntensity(intensity * bleedPercent * GetIntensityMultiplier() * intensityPercent);
	bleed->SetAttenuationRadius(reach * reachPercent);
	bleed->SetInnerConeAngle(bleedAngle.X * conePercent);
	bleed->SetOuterConeAngle(bleedAngle.Y * conePercent);
}

void AFlashlightItem::Activate_Implementation()
{
	Super::Activate_Implementation();
	workingTime = 0.f;
}

void AFlashlightItem::Update_Implementation(float deltaSeconds)
{
	Super::Update_Implementation(deltaSeconds);
	if (bActivated && !bFlickering)
	{
		UpdateLights();
		workingTime += deltaSeconds;
		if (workingTime >= GetFlickerCooldown())
		{
			workingTime = 0.f;
			if (FMath::FRand() <= GetFlickerChance())
			{
				Flicker();
			}
		}
	}
}

void AFlashlightItem::Deactivate_Implementation()
{
	Super::Deactivate_Implementation();

	if (bFlickering)
	{
		MulticastStopFlicker();
	}
}

void AFlashlightItem::OnRep_ActivatedUpdate()
{
	Super::OnRep_ActivatedUpdate();

	light->SetVisibility(bActivated);
	bleed->SetVisibility(bActivated);
}

void AFlashlightItem::Flicker()
{
	double cycle = FMath::DegreesToRadians(180.f) / flickerSpeed;
	int minOscilations = FMath::CeilToInt(flickerDuration.X / cycle);
	int maxOscilations = FMath::CeilToInt(flickerDuration.X / cycle);
	int numOscilations = FMath::RandRange(minOscilations, maxOscilations);
	float duration = cycle * numOscilations;
	float blackout = (FMath::FRand() <= GetBlackoutChance()) ? FMath::RandRange(blackoutDuration.X, blackoutDuration.Y) : 0.f;

	MulticastStartFlicker(GetFlickerStrength(), duration, blackout);
}

void AFlashlightItem::MulticastStartFlicker_Implementation(float loss, float duration, float blackout)
{
	flickeringTime = 0.f;
	FTimerDelegate flickerDelegate = FTimerDelegate::CreateUObject(this, &AFlashlightItem::UpdateFlicker, loss, duration, blackout);
	GetWorld()->GetTimerManager().SetTimer(flickerTimer, flickerDelegate, flickerDelta, true);
	bFlickering = true;
}

void AFlashlightItem::UpdateFlicker(float loss, float duration, float blackout)
{
	flickeringTime += flickerDelta;
	if (flickeringTime <= duration)
	{
		float currentLoss = loss * FMath::Abs(FMath::Sin(flickerSpeed * flickeringTime));
		UpdateLights(currentLoss);
	}
	else if (flickeringTime <= duration + blackout)
	{
		UpdateLights(1.f);
	}
	else if (HasAuthority())
	{
		MulticastStopFlicker();
	}
}

void AFlashlightItem::MulticastStopFlicker_Implementation()
{
	bFlickering = false;
	GetWorld()->GetTimerManager().ClearTimer(flickerTimer);
	UpdateLights(0.f);
}

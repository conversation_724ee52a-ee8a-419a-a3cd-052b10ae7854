{"FileVersion": 2, "EngineVersion": "5.5", "Version": 597, "PlatformAllowList": ["Win64", "<PERSON>", "IOS", "Linux", "LinuxArm64", "Android"], "VersionName": "1.10.2", "FriendlyName": "4Players ODIN", "Description": "Unreal integration plugin to integrate real-time chat technology into your game", "Category": "Other", "CreatedBy": "4Players GmbH", "CreatedByURL": "https://www.4players.io", "DocsURL": "https://www.4players.io/odin/sdk/unreal/", "MarketplaceURL": "com.epicgames.launcher://ue/marketplace/product/f45b045efc62416e9f6e6858519ab999", "SupportURL": "", "CanContainContent": false, "IsBetaVersion": false, "IsExperimentalVersion": false, "Installed": false, "Modules": [{"Name": "<PERSON><PERSON>", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON>efault", "PlatformAllowList": ["Win64", "<PERSON>", "IOS", "Linux", "LinuxArm64", "Android"], "AdditionalDependencies": ["AudioMixer", "AudioCapture"]}], "Plugins": [{"Name": "AudioCapture", "Enabled": true, "PlatformAllowList": ["Win64", "<PERSON>", "IOS", "Linux", "LinuxArm64", "Android"]}]}
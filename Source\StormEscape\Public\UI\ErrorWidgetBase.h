// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "Blueprint/UserWidget.h"
#include "Components/TextBlock.h"
#include "ErrorWidgetBase.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnErrorClosed);

/**
 * 
 */
UCLASS()
class STORMESCAPE_API UErrorWidgetBase : public UCommonActivatableWidget
{
	GENERATED_BODY()

public:

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UTextBlock* ErrorHead;

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	UTextBlock* ErrorBody;
	
	UPROPERTY(BlueprintAssignable, Category = "ErrorWidget")
	FOnErrorClosed OnErrorClosed;


	//add an inline init call here
	UFUNCTION(BlueprintCallable, Category = "ErrorWidget")
	void InitErrorWidget(const FString& ErrorHeadText, const FString& ErrorBodyText)
	{
		if (ErrorHead)
		{
			ErrorHead->SetText(FText::FromString(ErrorHeadText));
		}

		if (ErrorBody)
		{
			ErrorBody->SetText(FText::FromString(ErrorBodyText));
		}
	}

protected:
	//we need to first check the current input mode and mouse cursor state and save them and then reset them on destruct.

	bool OldCursorState;

	virtual void NativeConstruct() override
	{
		Super::NativeConstruct();

		//before settting the input mode store the old input mode and mouse cursor state
		
		// Set the input mode to UI only
		APlayerController* PlayerController = GetOwningPlayer();
		
		if (PlayerController)
		{
			OldCursorState = PlayerController->bShowMouseCursor;
			PlayerController->bShowMouseCursor = true;
		}
	}

	virtual void NativeDestruct() override
	{
		Super::NativeDestruct();

		// Restore the old input mode and mouse cursor state
		APlayerController* PlayerController = GetOwningPlayer();
		
		if (PlayerController)
		{
			PlayerController->bShowMouseCursor = OldCursorState;

			//if the old cursor state was false then set the input mode to game only
			if (!OldCursorState)
			{
				FInputModeGameOnly InputMode;
				PlayerController->SetInputMode(InputMode);
			}
		}
		
		OnErrorClosed.Broadcast();
	}
};

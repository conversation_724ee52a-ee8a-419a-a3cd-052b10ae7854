#pragma once

#include "CoreMinimal.h"
#include "Items/BaseItem.h"
#include "Interfaces/RepairInterface.h"
#include "BaseCap.generated.h"

class AGameplayVan;

UCLASS()
class STORMESCAPE_API ABaseCap : public ABaseItem, public IRepairInterface
{
	GENERATED_BODY()
	
public:
	ABaseCap();

	AGameplayVan* vanRef;

	UPROPERTY(Replicated)
	bool bIsOpen;

protected:
	virtual void BeginPlay() override;

	virtual void Complete_Implementation() override;

	FORCEINLINE bool RequiresTool_Implementation() override { return bRequiresToolBox; }

	FORCEINLINE bool IsSamePartType_Implementation(EVehiclePart InVehiclePart) override { return vehiclePart == InVehiclePart; }

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UStaticMeshComponent* capMesh;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Properties|Repairing")
	bool bRequiresToolBox;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties|Repairing")
	float timeToRemove;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties|Repairing")
	float timeToInsert;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Properties|Repairing")
	EVehiclePart vehiclePart;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Properties|Repairing")
	FTransform capOffset;

protected:
	void GetVanReference();

	void OpenCap();

	UFUNCTION(NetMulticast, Reliable)
	virtual void Multicast_Open();

	void CloseCap();

	UFUNCTION(NetMulticast, Reliable)
	virtual void Multicast_Close();

};
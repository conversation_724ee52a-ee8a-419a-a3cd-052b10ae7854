#pragma once

#include "CoreMinimal.h"
#include "Items/BaseItem.h"
#include "BaseToolbox.generated.h"

class UCameraComponent;
class UInteractComponent;

UCLASS()
class STORMESCAPE_API ABaseToolbox : public ABaseItem
{
	GENERATED_BODY()
	
public:
	ABaseToolbox();
	void Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;
	void Interrupt_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

private:
	void TraceVehicleParts(UInteractComponent* interactComponent);
};

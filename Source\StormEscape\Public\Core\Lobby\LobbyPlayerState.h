﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerState.h"
#include "LobbyPlayerState.generated.h"

/**
 * 
 */
UCLASS()
class STORMESCAPE_API ALobbyPlayerState : public APlayerState
{
	GENERATED_BODY()

public:
	
	UPROPERTY(ReplicatedUsing = OnRep_IsReady)
	bool bIsReady;

	UFUNCTION()
	void OnRep_IsReady();
	
	UFUNCTION(Server, Reliable)
	void ServerSetReady(bool bNewReady);

protected:
	
	virtual void GetLifetimeReplicatedProps(TArray<class FLifetimeProperty>& OutLifetimeProps) const override;

};

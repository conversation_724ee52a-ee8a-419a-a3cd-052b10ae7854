﻿#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "Blueprint/UserWidget.h"
#include "Data/PDA_GameplayMaps.h"
#include "MapSelectionPanel.generated.h"


/**
 * Panel that shows available maps and allows host to choose one.
 */
UCLASS()
class STORMESCAPE_API UMapSelectionPanel : public UCommonActivatableWidget
{
	GENERATED_BODY()

public:
	virtual void NativeConstruct() override;

	/** Set from Blueprint or dynamically with data asset */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Setup")
	UPDA_GameplayMaps* MapDataAsset;

	/** Called when map changes on server */
	UFUNCTION(BlueprintImplementableEvent)
	void OnCurrentMapChanged(const FPlayMapInfo& NewMap);
	
};

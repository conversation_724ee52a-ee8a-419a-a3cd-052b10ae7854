#include "Van/Battery.h"
#include "Van/BatterySpot.h"

ABattery::ABattery()
{
	timeToRemove = 3.f;
	timeToInsert = 3.f;
	timeToInstall = 3.f;
	bRequiresToolBox = true;
	vehiclePart = EVehiclePart::Battery;
	bIsRemovable = true;
	bCanBeRemoved = false;

	progressType = EProgressType::Persistent;
}

void ABattery::BeginPlay()
{
	Super::BeginPlay();

	if (vehiclePartSpot)
	{
		if (ABatterySpot* spot = Cast<ABatterySpot>(vehiclePartSpot))
		{
			spot->SetBatteryReference(this);
		}
	}
}

void ABattery::InsertPart()
{
	Super::InsertPart();

	if (vehiclePartSpot)
	{
		if (ABatterySpot* spot = Cast<ABatterySpot>(vehiclePartSpot))
		{
			spot->SetBatteryReference(this);
		}
	}
}

void ABattery::RemovePart()
{
	if (vehiclePartSpot)
	{
		if (ABatterySpot* spot = Cast<ABatterySpot>(vehiclePartSpot))
		{
			spot->SetBatteryReference(nullptr);
		}
	}

	Super::RemovePart();
}

void ABattery::Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	if (bIsHeld)
	{
		Super::Interact_Implementation(interactor, interactComponent);
	}
	else
	{
		if (bCanBeRemoved)
		{
			Super::Interact_Implementation(interactor, interactComponent);
		}
	}
}

void ABattery::HandleTerminalsInstalled()
{
	if(!bIsDamaged)
		InstallPart();
}

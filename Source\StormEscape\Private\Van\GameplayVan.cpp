#include "Van/GameplayVan.h"
#include "Items/BaseVehiclePart.h"
#include "Net/UnrealNetwork.h"
#include "Van/LiftButton.h"

AGameplayVan::AGameplayVan()
{
	PrimaryActorTick.bCanEverTick = true;
	bReplicates = true;

	USceneComponent* root = CreateDefaultSubobject<USceneComponent>("RootComponent");
	SetRootComponent(root);

	vanMesh = CreateDefaultSubobject<UStaticMeshComponent>("VanMesh");
	vanMesh->SetupAttachment(root);

	engineLid = CreateDefaultSubobject<UChildActorComponent>("EngineLid");
	engineLid->SetupAttachment(vanMesh);

	engineMesh = CreateDefaultSubobject<UStaticMeshComponent>("EngineMesh");
	engineMesh->SetupAttachment(vanMesh);
	engineMesh->SetCollisionResponseToChannel(ECC_Visibility, ECR_Ignore);

	frontWheelSpotR = CreateDefaultSubobject<UChildActorComponent>("FrontWheelSpotR");
	frontWheelSpotR->SetupAttachment(vanMesh);
	frontWheelSpotL = CreateDefaultSubobject<UChildActorComponent>("FrontWheelSpotL");
	frontWheelSpotL->SetupAttachment(vanMesh);
	backWheelSpotR = CreateDefaultSubobject<UChildActorComponent>("BackWheelSpotR");
	backWheelSpotR->SetupAttachment(vanMesh);
	backWheelSpotL = CreateDefaultSubobject<UChildActorComponent>("BackWheelSpotL");
	backWheelSpotL->SetupAttachment(vanMesh);

	frontWheelR = CreateDefaultSubobject<UChildActorComponent>("FrontWheelR");
	frontWheelR->SetupAttachment(frontWheelSpotR);

	frontWheelL = CreateDefaultSubobject<UChildActorComponent>("FrontWheelL");
	frontWheelL->SetupAttachment(frontWheelSpotL);

	backWheelR = CreateDefaultSubobject<UChildActorComponent>("BackWheelR");
	backWheelR->SetupAttachment(backWheelSpotR);

	backWheelL = CreateDefaultSubobject<UChildActorComponent>("BackWheelL");
	backWheelL->SetupAttachment(backWheelSpotL);

	fuelCap = CreateDefaultSubobject<UChildActorComponent>("FuelReservoir");
	fuelCap->SetupAttachment(vanMesh);

	coolantCap = CreateDefaultSubobject<UChildActorComponent>("Coolant");
	coolantCap->SetupAttachment(engineMesh);

	oilCap = CreateDefaultSubobject<UChildActorComponent>("OilReservoir");
	oilCap->SetupAttachment(engineMesh);

	batterySpot = CreateDefaultSubobject<UChildActorComponent>("BatterySpot");
	batterySpot->SetupAttachment(engineMesh);

	battery = CreateDefaultSubobject<UChildActorComponent>("Battery");
	battery->SetupAttachment(batterySpot);

	fuseBox = CreateDefaultSubobject<UChildActorComponent>("FuseBox");
	fuseBox->SetupAttachment(engineMesh);

	fuses = CreateDefaultSubobject<UChildActorComponent>("Fuses");
	fuses->SetupAttachment(fuseBox);

	liftButton = CreateDefaultSubobject<UChildActorComponent>("LiftButton");
	liftButton->SetupAttachment(vanMesh);

	bJackInstalled = false;
	bHasHealtyBattery = false;
	bEngineLidOpen = false;
	bDoorsOpen = false;
	bVanStarted = false;
	bHasHealtyFuses = false;

	maxFuel = 40.f;
	minFuelToStart = 8.f;
	fuel = 1.f;

	maxAntiFreeze = 20.f;
	minAntiFreezeToStart = 10.f;
	antiFreeze = 1.f;

	maxOil = 15.f;
	minOilToStart = 6.f;
	oil = 1.f;
}

void AGameplayVan::BeginPlay()
{
	Super::BeginPlay();
	
	InitializeComponents();
	UpdateFuelWidget(fuel);
	UpdateFreezeMaterial(antiFreeze / maxAntiFreeze);
	UpdateOilMaterial(oil / maxOil);
	EnableInteraction();
}

void AGameplayVan::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
}

void AGameplayVan::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(AGameplayVan, rotationOffset);
	DOREPLIFETIME(AGameplayVan, numberOfDamagedTires);
	DOREPLIFETIME(AGameplayVan, bJackInstalled);
	DOREPLIFETIME(AGameplayVan, bHasHealtyBattery);
	DOREPLIFETIME(AGameplayVan, bHasHealtyFuses);
	DOREPLIFETIME(AGameplayVan, bDoorsOpen);
	DOREPLIFETIME(AGameplayVan, bEngineLidOpen);
	DOREPLIFETIME(AGameplayVan, bVanStarted);
	DOREPLIFETIME(AGameplayVan, fuel);
	DOREPLIFETIME(AGameplayVan, antiFreeze);
	DOREPLIFETIME(AGameplayVan, oil);
}

void AGameplayVan::InitializeComponents()
{
	if (engineLid->GetChildActor())
		engineLid->GetChildActor()->SetOwner(this);

	if (frontWheelR->GetChildActor())
	{
		frontWheelR->GetChildActor()->SetOwner(this);
		frontWheelR->GetChildActor()->Tags.Add("TireFR");
	}

	if (frontWheelL->GetChildActor())
	{
		frontWheelL->GetChildActor()->SetOwner(this);
		frontWheelL->GetChildActor()->Tags.Add("TireFL");
	}

	if (backWheelR->GetChildActor())
	{
		backWheelR->GetChildActor()->SetOwner(this);
		backWheelR->GetChildActor()->Tags.Add("TireBR");
	}

	if (backWheelL->GetChildActor())
	{
		backWheelL->GetChildActor()->SetOwner(this);
		backWheelL->GetChildActor()->Tags.Add("TireBL");
	}


	if (frontWheelSpotR->GetChildActor())
	{
		frontWheelSpotR->GetChildActor()->SetOwner(this);
		frontWheelSpotR->GetChildActor()->Tags.Add("TireFR");
	}

	if (frontWheelSpotL->GetChildActor())
	{
		frontWheelSpotL->GetChildActor()->SetOwner(this);
		frontWheelSpotL->GetChildActor()->Tags.Add("TireFL");
	}

	if (backWheelSpotR->GetChildActor())
	{
		backWheelSpotR->GetChildActor()->SetOwner(this);
		backWheelSpotR->GetChildActor()->Tags.Add("TireBR");
	}

	if (backWheelSpotL->GetChildActor())
	{
		backWheelSpotL->GetChildActor()->SetOwner(this);
		backWheelSpotL->GetChildActor()->Tags.Add("TireBL");
	}

	if (fuelCap->GetChildActor())
		fuelCap->GetChildActor()->SetOwner(this);

	if (coolantCap->GetChildActor())
		coolantCap->GetChildActor()->SetOwner(this);

	if (oilCap->GetChildActor())
		oilCap->GetChildActor()->SetOwner(this);

	if (batterySpot->GetChildActor())
		batterySpot->GetChildActor()->SetOwner(this);

	if (battery->GetChildActor())
		batterySpot->GetChildActor()->SetOwner(this);

	if (liftButton->GetChildActor())
	{
		liftButton->GetChildActor()->SetOwner(this);

		if (ALiftButton* buttonRef = Cast<ALiftButton>(liftButton->GetChildActor()))
		{
			buttonRef->OnLiftButtonPressedDelegate.AddUniqueDynamic(this, &AGameplayVan::HandleLiftButtonPressed);
			if (bDoorsOpen)
				buttonRef->OpenDoor();
		}
	}

	if (fuseBox->GetChildActor())
		fuseBox->GetChildActor()->SetOwner(this);

	if (fuses->GetChildActor())
		fuses->GetChildActor()->SetOwner(this);

	OnPartRemovedDelegate.AddDynamic(this, &AGameplayVan::HandlePartRemoved);
	OnPartInstalledDelegate.AddDynamic(this, &AGameplayVan::HandlePartInstalled);
}

bool AGameplayVan::IsVanRepaired()
{
	if (numberOfDamagedTires > 0)
	{
		GEngine->AddOnScreenDebugMessage(2, 5.0f, FColor::Red, TEXT("Tires need to be repaired"));
		return false;
	}
	if (fuel < minFuelToStart)
	{
		GEngine->AddOnScreenDebugMessage(2, 5.0f, FColor::Red, TEXT("Not enough fuel to start"));
		return false;
	}
	if (antiFreeze < minAntiFreezeToStart)
	{
		GEngine->AddOnScreenDebugMessage(2, 5.0f, FColor::Red, TEXT("Not enough anti-freeze to start"));
		return false;
	}
	if (oil < minOilToStart)
	{
		GEngine->AddOnScreenDebugMessage(2, 5.0f, FColor::Red, TEXT("Not enough oil to start"));
		return false;
	}
	if (!bHasHealtyBattery)
	{
		GEngine->AddOnScreenDebugMessage(2, 5.0f, FColor::Red, TEXT("Battery need to be repaired"));
		return false;
	}
	if (!bHasHealtyFuses)
	{
		GEngine->AddOnScreenDebugMessage(2, 5.0f, FColor::Red, TEXT("Fuses need to be repaired"));
		return false;
	}
	if (bEngineLidOpen)
	{
		GEngine->AddOnScreenDebugMessage(2, 5.0f, FColor::Red, TEXT("Engine lid needs to be closed"));
		return false;
	}

	return true;
}

void AGameplayVan::FillFuelTank(float fuelAmount)
{
	if (!IsFuelTankFull())
	{
		fuel = FMath::Clamp(fuel + fuelAmount, 0.f, maxFuel);
		UpdateFuelWidget(fuel);
	}
}

bool AGameplayVan::IsFuelTankFull()
{
	return fuel >= maxFuel;
}

void AGameplayVan::FillFreezeTank(float freezeAmount)
{
	if (!IsFreezeTankFull())
	{
		antiFreeze = FMath::Clamp(antiFreeze + freezeAmount, 0.f, maxAntiFreeze);
		UpdateFreezeMaterial(antiFreeze / maxAntiFreeze);
	}
}

bool AGameplayVan::IsFreezeTankFull()
{
	return antiFreeze >= maxAntiFreeze;
}

void AGameplayVan::FillOilTank(float oilAmount)
{
	if (!IsOilTankFull())
	{
		oil = FMath::Clamp(oil + oilAmount, 0.f, maxOil);
		UpdateOilMaterial(oil / maxOil);
	}
}

bool AGameplayVan::IsOilTankFull()
{
	return oil >= maxOil;
}

void AGameplayVan::HandlePartDamaged(EVehiclePart partDamaged)
{
	if (partDamaged == EVehiclePart::Tire)
	{
		numberOfDamagedTires += 1;
	}

	if (partDamaged == EVehiclePart::Battery)
	{
		bHasHealtyBattery = false;
	}

	if (partDamaged == EVehiclePart::Fuses)
	{
		bHasHealtyFuses = false;
	}
}

void AGameplayVan::HandlePartRemoved(EVehiclePart partRemoved)
{
}

void AGameplayVan::HandlePartInstalled(EVehiclePart partInstalled)
{
	if (partInstalled == EVehiclePart::Tire)
	{
		HandleTireInstalled();
	}

	if (partInstalled == EVehiclePart::Battery)
	{
		bHasHealtyBattery = true;
	}

	if (partInstalled == EVehiclePart::Fuses)
	{
		bHasHealtyFuses = true;
	}
}

void AGameplayVan::HandleJackRemoved()
{
	bJackInstalled = false;
	LiftVan(this, 0.f, 1.f);
}

void AGameplayVan::HandleTireInstalled()
{
	if (numberOfDamagedTires > 0)
	{
		OnTireInstalledDelagate.Broadcast();
		numberOfDamagedTires -= 1;
	}
}

void AGameplayVan::HandleLiftButtonPressed(const bool bShouldOpen)
{
	bDoorsOpen = bShouldOpen;
	ToggleDoorsOpen(bShouldOpen);
}

void AGameplayVan::HandleEngineLidOpen(const bool bOpen)
{
	bEngineLidOpen = bOpen;
}
// Fill out your copyright notice in the Description page of Project Settings.


#include "Items/DefibrillatorItem.h"
#include "Components/InteractComponent.h"

ADefibrillatorItem::ADefibrillatorItem()
{
	bIsPickable = true;
	activationType = EActivationType::Hold;
	progressType = EProgressType::Activation;
	actionDuration = 3.f;

	maxDurability = 1.f;
	resurrectionCost = 1.f;
}

void ADefibrillatorItem::NotifyPickUp_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	Super::NotifyPickUp_Implementation(interactor, interactComponent);
	userComponent = interactComponent;
}

void ADefibrillatorItem::NotifyDrop_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	Super::NotifyDrop_Implementation(interactor, interactComponent);
	userComponent = nullptr;
}

void ADefibrillatorItem::Activate_Implementation()
{
	Super::Activate_Implementation();
	if (IsValid(userComponent))
	{
		userComponent->AddSpeedModifier(this, -speedReduction);
	}
}

void ADefibrillatorItem::Deactivate_Implementation()
{
	Super::Deactivate_Implementation();
	if (IsValid(userComponent) && !bIsComplete)
	{
		userComponent->RemoveSpeedModifier(this);
	}
}

void ADefibrillatorItem::Complete_Implementation()
{
	Super::Complete_Implementation();

	if (!IsValid(userComponent))
	{
		UE_LOG(LogItem, Error, TEXT("%s (Complete): invalid owner interact component reference."), *GetName());
		return;
	}

	userComponent->RemoveSpeedModifier(this);
	FHitResult hitResult;
	bool bHit = userComponent->GetTraceResult(shockRange, hitResult);
	AActor* actor = bHit ? hitResult.GetActor() : nullptr;
	if (!IsValid(actor) || !actor->GetClass()->ImplementsInterface(UDamageInterface::StaticClass()))
	{
		Multicast_OnShockReleased(hitResult);
		return;
	}

	if (IDamageInterface::Execute_GetHealthStatus(actor) == EActorHealthStatus::Dead)
	{
		IDamageInterface::Execute_Resurrect(actor, GetOwner(), healingAmount);
		Multicast_OnShockReleased(hitResult, IDamageInterface::Execute_GetHealthStatus(actor));
		durability -= resurrectionCost;
		OnRep_DurabilityUpdate();
	}
	else
	{
		IDamageInterface::Execute_Incapacitate(actor, GetOwner(), stunDuration, pushStrength);
		Multicast_OnShockReleased(hitResult, EActorHealthStatus::Incapacitated);
	}
}

void ADefibrillatorItem::Multicast_OnShockReleased_Implementation(const FHitResult& hitResult, EActorHealthStatus targetStatus)
{
	switch (targetStatus)
	{
		case EActorHealthStatus::None:
		{
			K2_OnInvalidTarget(IsValid(hitResult.GetActor()), hitResult);
			break;
		}
		
		case EActorHealthStatus::Incapacitated:
		{
			K2_OnTargetStunned(hitResult.GetActor());
			break;
		}
			
		default:
		{
			K2_OnTargetRevived(hitResult.GetActor(), targetStatus != EActorHealthStatus::Dead);
			break;
		}
	}
}

// AnimatableRetainerBox.h
#pragma once

#include "CoreMinimal.h"
#include "Components/RetainerBox.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Styling/SlateBrush.h"
#include "AnimatableRetainerBox.generated.h"

/** Structure to hold material parameter information */
USTRUCT(BlueprintType)
struct FRetainerBoxMaterialParam
{
    GENERATED_USTRUCT_BODY()

    /** The name of the material parameter */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Material Parameter")
    FName Name;

    /** The current value of the parameter */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Interp, Category = "Material Parameter")
    float CurrentValue;

    FRetainerBoxMaterialParam()
        : Name(NAME_None)
        , CurrentValue(0.0f)
    {
    }

    FRetainerBoxMaterialParam(FName InName, float InValue)
        : Name(InName)
        , CurrentValue(InValue)
    {
    }
};

/**
 * An enhanced RetainerBox that supports material animation through timeline
 * Uses a Brush property that auto-syncs with the Effect Material for animation
 * Features:
 * - Material parameter control through Blueprint
 * - Design-time preview support
 * - Automatic material instance management
 * - Performance optimization options
 */
UCLASS(Blueprintable, meta=(DisplayName = "Animatable Retainer Box"))
class STORMESCAPE_API UAnimatableRetainerBox : public URetainerBox
{
    GENERATED_BODY()

public:
    UAnimatableRetainerBox();

    /** The brush that syncs with the effect material for animation */
    UPROPERTY(EditAnywhere, Interp, Category = "Appearance", meta = (DisplayName = "Effect Brush"))
    FSlateBrush EffectBrush;

    /** Enable debug logging */
    UPROPERTY(EditAnywhere, Category = "Debug")
    bool bDebugMode;

    /** Material parameters that can be animated */
    UPROPERTY(EditAnywhere, Interp, Category = "Effect", meta = (DisplayName = "Material Parameters"))
    TArray<FRetainerBoxMaterialParam> MaterialParams;

    /** Get the current material instance */
    UFUNCTION(BlueprintCallable, Category = "Effect")
    UMaterialInstanceDynamic* GetMaterialInstance() const;

    /** Force refresh the material instance and its parameters */
    UFUNCTION(BlueprintCallable, Category = "Effect")
    void RefreshMaterial();

    /** Add a new material parameter that can be animated */
    UFUNCTION(BlueprintCallable, Category = "Effect")
    void AddParameter(FName ParamName, float DefaultValue = 0.0f);

    /** Get all scalar parameter names from the current material */
    UFUNCTION(BlueprintCallable, Category = "Effect")
    TArray<FName> GetAvailableParameters() const;

protected:
    //~ Begin UWidget Interface
    virtual void SynchronizeProperties() override;
    virtual TSharedRef<SWidget> RebuildWidget() override;
    //~ End UWidget Interface

    /** Create and initialize the dynamic material instance */
    void InitializeMaterialInstance();

    /** Sync the brush with the effect material */
    void SyncBrushWithMaterial();

    /** Update material parameters */
    void UpdateParameters();

    /** The dynamic material instance used for animation */
    UPROPERTY(Transient)
    UMaterialInstanceDynamic* MaterialInstance;

    /** Log debug information if debug mode is enabled */
    void LogDebug(const FString& Message) const;

    /** Called when the brush material changes */
    void OnBrushResourceChanged();
}; 
// Fill out your copyright notice in the Description page of Project Settings.


#include "Van/Workbench.h"
#include "BlueprintLibraries/UIHelperLibrary.h"

bool FWorkbenchZoneData::SpawnRandomItem(FItemSpawnData& outData, TSet<EItemCategory> priorities)
{
	if (itemPool.IsEmpty() || itemCount == slots.Num())
	{
		return false;
	}

	FRoulette roulette;
	for (const TPair<FName, FItemSpawnData>& data : itemPool)
	{
		if (data.Value.bAvailable)
		{
			roulette.AddTicket(data.Key, GetItemChance(data.Value, priorities));
		}
	}

	FRouletteTicket ticket = roulette.Spin();
	if (ticket.bValid)
	{
		outData = itemPool[ticket.value.name];
		slots[itemCount]->SpawnItem(outData);
		itemCount++;
		return true;
	}

	return false;
}

void FWorkbenchZoneData::RegisterItemData(FName itemName, FItemSpawnData data)
{
	itemPool.Add(itemName, data);
	categoryPool.Add(data.itemCategory);
}

float FWorkbenchZoneData::GetItemChance(const FItemSpawnData& data, TSet<EItemCategory> priorities)
{
	bool bHasPriority = priorities.IsEmpty() || priorities.Contains(data.itemCategory);
	return bHasPriority ? data.spawnWeight : 0.f;
}

// Sets default values
AWorkbench::AWorkbench()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = false;
	bReplicates = true;
	bSpawnItems = true;

	mesh = CreateDefaultSubobject<UStaticMeshComponent>("mesh");
	SetRootComponent(mesh);
}

// Called when the game starts or when spawned
void AWorkbench::BeginPlay()
{
	Super::BeginPlay();

	Assemble();
	if (bSpawnItems && HasAuthority())
	{
		int numPlayers = UUIHelperLibrary::GetPlayerCount(this);
		GenerateItems(numPlayers);
		UE_LOG(LogTemp, Display, TEXT("Workbench: items spawned for %d players"), numPlayers)
	}
}

void AWorkbench::Assemble()
{
	if (!IsValid(settings))
	{
		UE_LOG(LogTemp, Error, TEXT("Workbench settings missing"))
		return;
	}

	zoneMap.Add(EItemSize::Small, FWorkbenchZoneData(settings->pegboardSettings));
	zoneMap.Add(EItemSize::Medium, FWorkbenchZoneData(settings->tableSettings));
	zoneMap.Add(EItemSize::Large, FWorkbenchZoneData(settings->underTableSettings));

	for (const TPair<FName, FItemSpawnData>& entry : settings->itemPool)
	{
		if (zoneMap.Contains(entry.Value.itemSize) && entry.Value.bAvailable)
		{
			zoneMap[entry.Value.itemSize].RegisterItemData(entry.Key, entry.Value);
		}
	}

	for (UActorComponent* component : GetComponents())
	{
		UItemHookComponent* slot = Cast<UItemHookComponent>(component);
		if (IsValid(slot) && zoneMap.Contains(slot->GetItemSize()))
		{
			zoneMap[slot->GetItemSize()].RegisterSlot(slot);
		}
	}
}

void AWorkbench::GenerateItems(int numPlayers)
{
	int itemCount = IsValid(settings) ? settings->GetItemCount(numPlayers) : 2 * numPlayers;
	TSet<EItemSize> sizePool;
	zoneMap.GetKeys(sizePool);

	for (int i = 1; i <= itemCount; i++)
	{
		GenerateItem(sizePool);
	}
}

void AWorkbench::GenerateItem(TSet<EItemSize> pool)
{
	if (pool.IsEmpty())
	{
		return;
	}

	EItemSize size = GetRandomSize(pool);
	if (zoneMap.Contains(size))
	{
		TSet<EItemCategory> priorities = {};
		for (const EItemCategory category : zoneMap[size].GetCategories())
		{
			if (!categoryCountMap.Contains(category) || categoryCountMap[category] < 2)
			{
				priorities.Add(category);
			}
		}


		FItemSpawnData outData;
		if (zoneMap[size].SpawnRandomItem(outData, priorities))
		{
			categoryCountMap.FindOrAdd(outData.itemCategory)++;
		}
		else
		{
			pool.Remove(size);
			GenerateItem(pool);
		}
	}
}

EItemSize AWorkbench::GetRandomSize(TSet<EItemSize> pool) const
{
	FRoulette roulette;
	for (EItemSize size : pool)
	{
		if (zoneMap.Contains(size))
		{
			roulette.AddTicket((uint8)size, zoneMap[size].spawnChance);
		}
	}

	FRouletteTicket ticket = roulette.Spin();
	return ticket.bValid ? (EItemSize)ticket.value.baseEnum : EItemSize::Small;
}


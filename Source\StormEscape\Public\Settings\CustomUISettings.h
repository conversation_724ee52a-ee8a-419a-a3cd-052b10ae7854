// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DeveloperSettings.h"
#include "UI/LoadingScreenWidget.h"
#include "CommonActivatableWidget.h"
#include "CustomUISettings.generated.h"

class UErrorWidgetBase;
/**
 * 
 */
UCLASS(Config = Game, defaultconfig, meta = (DisplayName = "Custom UI Settings"))
class STORMESCAPE_API UCustomUISettings : public UDeveloperSettings
{
	GENERATED_BODY()
	
public:
	/* The widget class to be used as a loading screen */
	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "General", AdvancedDisplay)
	TSubclassOf<ULoadingScreenWidget> LoadingScreen;

	/* The widget class to be used as a loading screen for the server host */
	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "General", AdvancedDisplay)
	TSubclassOf<ULoadingScreenWidget> HostLoadingScreen;

	/* The widget class to be used as an Error Popup */
	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "General", AdvancedDisplay)
	TSubclassOf<UErrorWidgetBase> ErrorWidgetClass;
		
	//widget to be used as a Pause menu
	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "General", AdvancedDisplay)
	TSubclassOf<UCommonActivatableWidget> PauseMenuWidgetClass;
};

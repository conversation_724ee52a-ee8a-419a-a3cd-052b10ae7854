#include "UI/UIManager.h"

#include "CommonActivatableWidget.h"
#include "Blueprint/WidgetBlueprintLibrary.h"
#include "BlueprintLibraries/UIHelperLibrary.h"
#include "Core/StormEscapeGameInstance.h"
#include "UI/BaseMainLayerUI.h"
#include "Widgets/CommonActivatableWidgetContainer.h"
#include "InputMappingContext.h"
#include "EnhancedInputSubsystems.h"
#include "StormEscapePlayerController.h"

TWeakObjectPtr<UBaseMainLayerUI> UUIManager::MainLayerUI = nullptr;
TWeakObjectPtr<APlayerController> UUIManager::CachedPC = nullptr;
TWeakObjectPtr<UInputMappingContext> UUIManager::GameInputContext = nullptr;
TWeakObjectPtr<UInputMappingContext> UUIManager::UIInputContext = nullptr;
EInputContextState UUIManager::CurrentInputContextState = EInputContextState::None;

void UUIManager::Initialize(APlayerController* PlayerController, UBaseMainLayerUI* MainLayerUIInstance)
{
	MainLayerUI = MainLayerUIInstance;
	CachedPC = PlayerController;

	UE_LOG(LogTemp, Warning, TEXT("UIManager Initialized"));

	if (!PlayerController)
	{
		return;
	}

	// Initialize Enhanced Input mapping contexts
	if (AStormEscapePlayerController* StormPC = Cast<AStormEscapePlayerController>(PlayerController))
	{
		GameInputContext = StormPC->GetGameInputMappingContext();
		UIInputContext = StormPC->GetUIInputMappingContext();

		if (GameInputContext.IsValid() && UIInputContext.IsValid())
		{
			// Start with game input context as default
			CurrentInputContextState = EInputContextState::Game;
			UE_LOG(LogTemp, Log, TEXT("UIManager: Enhanced Input contexts initialized successfully"));
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("UIManager: Failed to initialize Enhanced Input contexts"));
		}
	}

	//GetGameInstance and check if bWasKicked is true and if so call the error popup
	if (UStormEscapeGameInstance* GameInstance = UStormEscapeGameInstance::GetActive_GameInstance(PlayerController))
	{
		if (GameInstance->bWasKicked)
		{
			UE_LOG(LogTemp, Warning, TEXT("Player was kicked. Showing error popup."));
			UUIHelperLibrary::ShowErrorPopup("Disconnected",GameInstance->KickReason, PlayerController);
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to get GameInstance from PlayerController"));
	}
}

UCommonActivatableWidget* UUIManager::AddWidgetToStack(UObject* WorldContextObject,
	TSubclassOf<UCommonActivatableWidget> WidgetClass,
	EWidgetLayer TargetLayer,
	EInputModeType InputMode,
	bool bShowMouseCursor,
	bool bClearCurrentStack)
{
	UE_LOG(LogTemp, Warning, TEXT("Adding widget to stack"));
	if (!MainLayerUI.IsValid() || !CachedPC.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("UUIManager not initialized. Call Initialize() from PlayerController."));
		return nullptr;
	}

	UCommonActivatableWidgetStack* TargetStack = GetTargetStack(TargetLayer);
	if (!TargetStack)
	{
		UE_LOG(LogTemp, Warning, TEXT("Invalid widget stack for layer %d"), (int32)TargetLayer);
		return nullptr;
	}

	if (bClearCurrentStack)
	{
		TargetStack->ClearWidgets();
	}

	UCommonActivatableWidget* Widget = TargetStack->AddWidget(WidgetClass);

	// Bind to widget destruction for automatic input context switching
	if (Widget && (TargetLayer == EWidgetLayer::Menu || TargetLayer == EWidgetLayer::Popup || TargetLayer == EWidgetLayer::Modal))
	{
		BindToWidgetDestruction(Widget);
	}

	// Set input mode - IMPORTANT: Use GameAndUI for Enhanced Input to work properly with UI
	switch (InputMode)
	{
	case EInputModeType::UIOnly:
		// For Enhanced Input + CommonUI, use GameAndUI instead of UIOnly to prevent input blocking
		UWidgetBlueprintLibrary::SetInputMode_GameAndUIEx(CachedPC.Get(), Widget, EMouseLockMode::DoNotLock, false);
		UE_LOG(LogTemp, Log, TEXT("UIManager: Set input mode to Game & UI (Enhanced Input compatible)"));
		break;
	case EInputModeType::GameOnly:
		UWidgetBlueprintLibrary::SetInputMode_GameOnly(CachedPC.Get());
		break;
	case EInputModeType::GameAndUI:
		UWidgetBlueprintLibrary::SetInputMode_GameAndUIEx(CachedPC.Get(), Widget, EMouseLockMode::DoNotLock, false);
		break;
	}

	CachedPC->bShowMouseCursor = bShowMouseCursor;

	// Automatically evaluate and update Enhanced Input context based on new UI state
	UpdateInputContextForUIState();

	// Ensure proper focus for gamepad navigation (with small delay to ensure widget is fully added)
	if (Widget && (TargetLayer == EWidgetLayer::Menu || TargetLayer == EWidgetLayer::Popup || TargetLayer == EWidgetLayer::Modal))
	{
		// Use a timer to ensure focus is set after the widget is fully constructed
		if (UWorld* World = Widget->GetWorld())
		{
			FTimerHandle FocusTimerHandle;
			World->GetTimerManager().SetTimer(FocusTimerHandle, []()
			{
				UUIManager::EnsureUIFocus();
			}, 0.1f, false);
		}
	}

	return Widget;
}

void UUIManager::SetInputMode(UObject* WorldContextObject, EInputModeType InputMode, bool bShowMouseCursor,UUserWidget* Widget)
{
	if (!MainLayerUI.IsValid() || !CachedPC.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("UUIManager not initialized. Call Initialize() from PlayerController."));
		return;
	}

	// Set input mode
	switch (InputMode)
	{
	case EInputModeType::UIOnly:
		UWidgetBlueprintLibrary::SetInputMode_UIOnlyEx(CachedPC.Get(), Widget, EMouseLockMode::DoNotLock, false);
		break;
	case EInputModeType::GameOnly:
		UWidgetBlueprintLibrary::SetInputMode_GameOnly(CachedPC.Get());
		break;
	case EInputModeType::GameAndUI:
		UWidgetBlueprintLibrary::SetInputMode_GameAndUIEx(CachedPC.Get(), Widget, EMouseLockMode::DoNotLock, false);
		break;
	}
	
	CachedPC->bShowMouseCursor = bShowMouseCursor;
}

UCommonActivatableWidgetStack* UUIManager::GetTargetStack(EWidgetLayer Layer)
{
	if (!MainLayerUI.IsValid())
		return nullptr;

	switch (Layer)
	{
	case EWidgetLayer::GameUI:
		return MainLayerUI->GameUIStack;
	case EWidgetLayer::Menu:
		return MainLayerUI->MenuStack;
	case EWidgetLayer::Popup:
		return MainLayerUI->PopupStack;
	case EWidgetLayer::Modal:
		return MainLayerUI->ModalStack;
	default:
		return nullptr;
	}
}

void UUIManager::EvaluateInputContext()
{
	UpdateInputContextForUIState();
}

void UUIManager::ForceGameInputContext()
{
	SwitchToGameInputContext();
}

void UUIManager::ForceUIInputContext()
{
	SwitchToUIInputContext();
}

void UUIManager::AddUIInputContextOverlay()
{
	// DEPRECATED: This overlay approach caused movement overlap issues
	// Use SwitchToUIInputContext() instead for proper context isolation
	UE_LOG(LogTemp, Warning, TEXT("UIManager: AddUIInputContextOverlay is deprecated. Use SwitchToUIInputContext() instead."));
	SwitchToUIInputContext();
}

void UUIManager::TestInputAction(const FString& ActionName)
{
	if (!CachedPC.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("UIManager: Cannot test input action - no cached player controller"));
		return;
	}

	if (UEnhancedInputLocalPlayerSubsystem* InputSubsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(CachedPC->GetLocalPlayer()))
	{
		UE_LOG(LogTemp, Log, TEXT("UIManager: Testing availability of action: %s"), *ActionName);

		// Check if the action exists in our known contexts
		bool bFoundAction = false;

		// Check game context
		if (GameInputContext.IsValid())
		{
			UE_LOG(LogTemp, Log, TEXT("UIManager: Checking game context: %s"), *GameInputContext->GetName());
			for (const FEnhancedActionKeyMapping& Mapping : GameInputContext->GetMappings())
			{
				if (Mapping.Action && Mapping.Action->GetName().Contains(ActionName))
				{
					UE_LOG(LogTemp, Log, TEXT("UIManager: FOUND ACTION: %s in game context"), *Mapping.Action->GetName());
					bFoundAction = true;
				}
			}
		}

		// Check UI context
		if (UIInputContext.IsValid())
		{
			UE_LOG(LogTemp, Log, TEXT("UIManager: Checking UI context: %s"), *UIInputContext->GetName());
			for (const FEnhancedActionKeyMapping& Mapping : UIInputContext->GetMappings())
			{
				if (Mapping.Action && Mapping.Action->GetName().Contains(ActionName))
				{
					UE_LOG(LogTemp, Log, TEXT("UIManager: FOUND ACTION: %s in UI context"), *Mapping.Action->GetName());
					bFoundAction = true;
				}
			}
		}

		if (!bFoundAction)
		{
			UE_LOG(LogTemp, Warning, TEXT("UIManager: Action '%s' NOT FOUND in any known context!"), *ActionName);
		}
	}
}

void UUIManager::DebugInputState()
{
	if (!CachedPC.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("UIManager: Cannot debug input state - no cached player controller"));
		return;
	}

	UE_LOG(LogTemp, Log, TEXT("=== UIManager Input State Debug ==="));
	UE_LOG(LogTemp, Log, TEXT("Current Input Context State: %d"), (int32)CurrentInputContextState);
	UE_LOG(LogTemp, Log, TEXT("Show Mouse Cursor: %s"), CachedPC->bShowMouseCursor ? TEXT("true") : TEXT("false"));

	// Check basic input state
	UE_LOG(LogTemp, Log, TEXT("Player Controller is valid: %s"), CachedPC.IsValid() ? TEXT("true") : TEXT("false"));

	// Check widget focus
	if (MainLayerUI.IsValid())
	{
		UE_LOG(LogTemp, Log, TEXT("MainLayerUI is valid"));
		if (MainLayerUI->MenuStack)
		{
			UCommonActivatableWidget* ActiveWidget = MainLayerUI->MenuStack->GetActiveWidget();
			UE_LOG(LogTemp, Log, TEXT("Active Menu Widget: %s"), ActiveWidget ? *ActiveWidget->GetName() : TEXT("None"));
			if (ActiveWidget)
			{
				UE_LOG(LogTemp, Log, TEXT("Widget Has Focus: %s"), ActiveWidget->HasUserFocus(CachedPC.Get()) ? TEXT("true") : TEXT("false"));
				UE_LOG(LogTemp, Log, TEXT("Widget Is Focusable: %s"), ActiveWidget->GetIsEnabled() ? TEXT("true") : TEXT("false"));
			}
		}
	}

	UE_LOG(LogTemp, Log, TEXT("=== End Debug ==="));
}

void UUIManager::EnsureUIFocus()
{
	if (!CachedPC.IsValid() || !MainLayerUI.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("UIManager: Cannot ensure UI focus - invalid references"));
		return;
	}

	// Find the active widget and ensure it has focus for gamepad navigation
	UCommonActivatableWidget* ActiveWidget = nullptr;

	// Check stacks in priority order: Modal > Popup > Menu
	if (MainLayerUI->ModalStack && MainLayerUI->ModalStack->GetActiveWidget())
	{
		ActiveWidget = MainLayerUI->ModalStack->GetActiveWidget();
	}
	else if (MainLayerUI->PopupStack && MainLayerUI->PopupStack->GetActiveWidget())
	{
		ActiveWidget = MainLayerUI->PopupStack->GetActiveWidget();
	}
	else if (MainLayerUI->MenuStack && MainLayerUI->MenuStack->GetActiveWidget())
	{
		ActiveWidget = MainLayerUI->MenuStack->GetActiveWidget();
	}

	if (ActiveWidget)
	{
		// Set focus to the active widget for gamepad navigation
		ActiveWidget->SetKeyboardFocus();
		UE_LOG(LogTemp, Log, TEXT("UIManager: Set focus to active widget: %s"), *ActiveWidget->GetName());
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("UIManager: No active UI widget found to focus"));
	}
}

void UUIManager::UpdateInputContextForUIState()
{
	if (!CachedPC.IsValid() || !GameInputContext.IsValid() || !UIInputContext.IsValid())
	{
		return;
	}

	bool bHasActiveUI = HasActiveUIWidgets();
	EInputContextState DesiredState = bHasActiveUI ? EInputContextState::UI : EInputContextState::Game;

	// Only switch if the desired state is different from current state
	if (DesiredState != CurrentInputContextState)
	{
		if (DesiredState == EInputContextState::UI)
		{
			// Use the proper UI context switching that preserves game context
			SwitchToUIInputContext();
		}
		else
		{
			SwitchToGameInputContext();
		}
	}
}

void UUIManager::SwitchToGameInputContext()
{
	if (!CachedPC.IsValid() || !GameInputContext.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("UIManager: Cannot switch to game input context - invalid references"));
		return;
	}

	if (UEnhancedInputLocalPlayerSubsystem* InputSubsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(CachedPC->GetLocalPlayer()))
	{
		// Remove UI context completely
		if (UIInputContext.IsValid())
		{
			InputSubsystem->RemoveMappingContext(UIInputContext.Get());
			UE_LOG(LogTemp, Log, TEXT("UIManager: Removed UI input context"));
		}

		// Restore game context as the only active context
		InputSubsystem->AddMappingContext(GameInputContext.Get(), 0);
		UE_LOG(LogTemp, Log, TEXT("UIManager: Restored game input context"));

		CurrentInputContextState = EInputContextState::Game;
		UE_LOG(LogTemp, Log, TEXT("UIManager: Switched to Game-only input context (IMC_FirstPersonMappingContext)"));

		// Log game context actions for debugging
		if (GameInputContext.IsValid())
		{
			UE_LOG(LogTemp, Log, TEXT("UIManager: Game Context Name: %s"), *GameInputContext->GetName());
			UE_LOG(LogTemp, Log, TEXT("UIManager: Available Game Actions:"));
			for (const FEnhancedActionKeyMapping& Mapping : GameInputContext->GetMappings())
			{
				if (Mapping.Action)
				{
					UE_LOG(LogTemp, Log, TEXT("  - %s"), *Mapping.Action->GetName());
				}
			}
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("UIManager: Failed to get Enhanced Input subsystem for game context switch"));
	}
}

void UUIManager::SwitchToUIInputContext()
{
	if (!CachedPC.IsValid() || !UIInputContext.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("UIManager: Cannot switch to UI input context - invalid references"));
		return;
	}

	if (UEnhancedInputLocalPlayerSubsystem* InputSubsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(CachedPC->GetLocalPlayer()))
	{
		// PROPER CONTEXT SWITCHING: Remove game context to prevent player movement during UI
		if (GameInputContext.IsValid())
		{
			InputSubsystem->RemoveMappingContext(GameInputContext.Get());
			UE_LOG(LogTemp, Log, TEXT("UIManager: Removed game input context to prevent movement overlap"));
		}

		// Add UI context as the only active context
		InputSubsystem->AddMappingContext(UIInputContext.Get(), 0);

		CurrentInputContextState = EInputContextState::UI;
		UE_LOG(LogTemp, Log, TEXT("UIManager: Switched to UI-only input context (IM_UIControls)"));

		// Log the UI context actions for debugging
		if (UIInputContext.IsValid())
		{
			UE_LOG(LogTemp, Log, TEXT("UIManager: UI Context Name: %s"), *UIInputContext->GetName());
			UE_LOG(LogTemp, Log, TEXT("UIManager: Available UI Actions:"));
			for (const FEnhancedActionKeyMapping& Mapping : UIInputContext->GetMappings())
			{
				if (Mapping.Action)
				{
					UE_LOG(LogTemp, Log, TEXT("  - %s"), *Mapping.Action->GetName());
				}
			}
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("UIManager: Failed to get Enhanced Input subsystem for UI context switch"));
	}
}

bool UUIManager::HasActiveUIWidgets()
{
	if (!MainLayerUI.IsValid())
	{
		return false;
	}

	// Check if Menu, Popup, or Modal stacks have any active widgets
	// GameUI stack is not considered "active UI" for input context switching
	bool bHasMenuWidgets = MainLayerUI->MenuStack && MainLayerUI->MenuStack->GetActiveWidget() != nullptr;
	bool bHasPopupWidgets = MainLayerUI->PopupStack && MainLayerUI->PopupStack->GetActiveWidget() != nullptr;
	bool bHasModalWidgets = MainLayerUI->ModalStack && MainLayerUI->ModalStack->GetActiveWidget() != nullptr;

	return bHasMenuWidgets || bHasPopupWidgets || bHasModalWidgets;
}

void UUIManager::BindToWidgetDestruction(UCommonActivatableWidget* Widget)
{
	if (!Widget)
	{
		return;
	}

	// For now, we'll rely on the input context evaluation being called when widgets are added
	// This ensures the system works correctly when widgets are opened
	// Future enhancement: implement proper widget destruction detection
	// The system will still function correctly, just with slightly delayed context switching on widget close
}

#include "UI/UIManager.h"

#include "CommonActivatableWidget.h"
#include "Blueprint/WidgetBlueprintLibrary.h"
#include "BlueprintLibraries/UIHelperLibrary.h"
#include "Core/StormEscapeGameInstance.h"
#include "UI/BaseMainLayerUI.h"
#include "Widgets/CommonActivatableWidgetContainer.h"
#include "InputMappingContext.h"
#include "EnhancedInputSubsystems.h"
#include "StormEscapePlayerController.h"

TWeakObjectPtr<UBaseMainLayerUI> UUIManager::MainLayerUI = nullptr;
TWeakObjectPtr<APlayerController> UUIManager::CachedPC = nullptr;
TWeakObjectPtr<UInputMappingContext> UUIManager::GameInputContext = nullptr;
TWeakObjectPtr<UInputMappingContext> UUIManager::UIInputContext = nullptr;
EInputContextState UUIManager::CurrentInputContextState = EInputContextState::None;

void UUIManager::Initialize(APlayerController* PlayerController, UBaseMainLayerUI* MainLayerUIInstance)
{
	MainLayerUI = MainLayerUIInstance;
	CachedPC = PlayerController;

	UE_LOG(LogTemp, Warning, TEXT("UIManager Initialized"));

	if (!PlayerController)
	{
		return;
	}

	// Initialize Enhanced Input mapping contexts
	if (AStormEscapePlayerController* StormPC = Cast<AStormEscapePlayerController>(PlayerController))
	{
		GameInputContext = StormPC->GetGameInputMappingContext();
		UIInputContext = StormPC->GetUIInputMappingContext();

		if (GameInputContext.IsValid() && UIInputContext.IsValid())
		{
			// Start with game input context as default
			CurrentInputContextState = EInputContextState::Game;
			UE_LOG(LogTemp, Log, TEXT("UIManager: Enhanced Input contexts initialized successfully"));
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("UIManager: Failed to initialize Enhanced Input contexts"));
		}
	}

	//GetGameInstance and check if bWasKicked is true and if so call the error popup
	if (UStormEscapeGameInstance* GameInstance = UStormEscapeGameInstance::GetActive_GameInstance(PlayerController))
	{
		if (GameInstance->bWasKicked)
		{
			UE_LOG(LogTemp, Warning, TEXT("Player was kicked. Showing error popup."));
			UUIHelperLibrary::ShowErrorPopup("Disconnected",GameInstance->KickReason, PlayerController);
		}
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to get GameInstance from PlayerController"));
	}
}

UCommonActivatableWidget* UUIManager::AddWidgetToStack(UObject* WorldContextObject,
	TSubclassOf<UCommonActivatableWidget> WidgetClass,
	EWidgetLayer TargetLayer,
	EInputModeType InputMode,
	bool bShowMouseCursor,
	bool bClearCurrentStack)
{
	UE_LOG(LogTemp, Warning, TEXT("Adding widget to stack"));
	if (!MainLayerUI.IsValid() || !CachedPC.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("UUIManager not initialized. Call Initialize() from PlayerController."));
		return nullptr;
	}

	UCommonActivatableWidgetStack* TargetStack = GetTargetStack(TargetLayer);
	if (!TargetStack)
	{
		UE_LOG(LogTemp, Warning, TEXT("Invalid widget stack for layer %d"), (int32)TargetLayer);
		return nullptr;
	}

	if (bClearCurrentStack)
	{
		TargetStack->ClearWidgets();
	}

	UCommonActivatableWidget* Widget = TargetStack->AddWidget(WidgetClass);

	// Bind to widget destruction for automatic input context switching
	if (Widget && (TargetLayer == EWidgetLayer::Menu || TargetLayer == EWidgetLayer::Popup || TargetLayer == EWidgetLayer::Modal))
	{
		BindToWidgetDestruction(Widget);
	}

	// Set input mode
	switch (InputMode)
	{
	case EInputModeType::UIOnly:
		UWidgetBlueprintLibrary::SetInputMode_UIOnlyEx(CachedPC.Get(), Widget, EMouseLockMode::DoNotLock, false);
		break;
	case EInputModeType::GameOnly:
		UWidgetBlueprintLibrary::SetInputMode_GameOnly(CachedPC.Get());
		break;
	case EInputModeType::GameAndUI:
		UWidgetBlueprintLibrary::SetInputMode_GameAndUIEx(CachedPC.Get(), Widget, EMouseLockMode::DoNotLock, false);
		break;
	}

	CachedPC->bShowMouseCursor = bShowMouseCursor;

	// Automatically evaluate and update Enhanced Input context based on new UI state
	UpdateInputContextForUIState();

	return Widget;
}

void UUIManager::SetInputMode(UObject* WorldContextObject, EInputModeType InputMode, bool bShowMouseCursor,UUserWidget* Widget)
{
	if (!MainLayerUI.IsValid() || !CachedPC.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("UUIManager not initialized. Call Initialize() from PlayerController."));
		return;
	}

	// Set input mode
	switch (InputMode)
	{
	case EInputModeType::UIOnly:
		UWidgetBlueprintLibrary::SetInputMode_UIOnlyEx(CachedPC.Get(), Widget, EMouseLockMode::DoNotLock, false);
		break;
	case EInputModeType::GameOnly:
		UWidgetBlueprintLibrary::SetInputMode_GameOnly(CachedPC.Get());
		break;
	case EInputModeType::GameAndUI:
		UWidgetBlueprintLibrary::SetInputMode_GameAndUIEx(CachedPC.Get(), Widget, EMouseLockMode::DoNotLock, false);
		break;
	}
	
	CachedPC->bShowMouseCursor = bShowMouseCursor;
}

UCommonActivatableWidgetStack* UUIManager::GetTargetStack(EWidgetLayer Layer)
{
	if (!MainLayerUI.IsValid())
		return nullptr;

	switch (Layer)
	{
	case EWidgetLayer::GameUI:
		return MainLayerUI->GameUIStack;
	case EWidgetLayer::Menu:
		return MainLayerUI->MenuStack;
	case EWidgetLayer::Popup:
		return MainLayerUI->PopupStack;
	case EWidgetLayer::Modal:
		return MainLayerUI->ModalStack;
	default:
		return nullptr;
	}
}

void UUIManager::EvaluateInputContext()
{
	UpdateInputContextForUIState();
}

void UUIManager::UpdateInputContextForUIState()
{
	if (!CachedPC.IsValid() || !GameInputContext.IsValid() || !UIInputContext.IsValid())
	{
		return;
	}

	bool bHasActiveUI = HasActiveUIWidgets();
	EInputContextState DesiredState = bHasActiveUI ? EInputContextState::UI : EInputContextState::Game;

	// Only switch if the desired state is different from current state
	if (DesiredState != CurrentInputContextState)
	{
		if (DesiredState == EInputContextState::UI)
		{
			SwitchToUIInputContext();
		}
		else
		{
			SwitchToGameInputContext();
		}
	}
}

void UUIManager::SwitchToGameInputContext()
{
	if (!CachedPC.IsValid() || !GameInputContext.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("UIManager: Cannot switch to game input context - invalid references"));
		return;
	}

	if (UEnhancedInputLocalPlayerSubsystem* InputSubsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(CachedPC->GetLocalPlayer()))
	{
		// Remove UI context and add game context
		if (UIInputContext.IsValid())
		{
			InputSubsystem->RemoveMappingContext(UIInputContext.Get());
		}
		InputSubsystem->AddMappingContext(GameInputContext.Get(), 0);

		CurrentInputContextState = EInputContextState::Game;
		UE_LOG(LogTemp, Log, TEXT("UIManager: Switched to Game input context (IMC_FirstPersonMappingContext)"));
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("UIManager: Failed to get Enhanced Input subsystem for game context switch"));
	}
}

void UUIManager::SwitchToUIInputContext()
{
	if (!CachedPC.IsValid() || !UIInputContext.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("UIManager: Cannot switch to UI input context - invalid references"));
		return;
	}

	if (UEnhancedInputLocalPlayerSubsystem* InputSubsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(CachedPC->GetLocalPlayer()))
	{
		// Remove game context and add UI context
		if (GameInputContext.IsValid())
		{
			InputSubsystem->RemoveMappingContext(GameInputContext.Get());
		}
		InputSubsystem->AddMappingContext(UIInputContext.Get(), 0);

		CurrentInputContextState = EInputContextState::UI;
		UE_LOG(LogTemp, Log, TEXT("UIManager: Switched to UI input context (IM_UIControls)"));
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("UIManager: Failed to get Enhanced Input subsystem for UI context switch"));
	}
}

bool UUIManager::HasActiveUIWidgets()
{
	if (!MainLayerUI.IsValid())
	{
		return false;
	}

	// Check if Menu, Popup, or Modal stacks have any active widgets
	// GameUI stack is not considered "active UI" for input context switching
	bool bHasMenuWidgets = MainLayerUI->MenuStack && MainLayerUI->MenuStack->GetActiveWidget() != nullptr;
	bool bHasPopupWidgets = MainLayerUI->PopupStack && MainLayerUI->PopupStack->GetActiveWidget() != nullptr;
	bool bHasModalWidgets = MainLayerUI->ModalStack && MainLayerUI->ModalStack->GetActiveWidget() != nullptr;

	return bHasMenuWidgets || bHasPopupWidgets || bHasModalWidgets;
}

void UUIManager::BindToWidgetDestruction(UCommonActivatableWidget* Widget)
{
	if (!Widget)
	{
		return;
	}

	// Create a lambda that will be called when the widget is destroyed
	// This lambda captures nothing and uses static methods to avoid issues with object lifetime
	Widget->OnDeactivated().AddLambda([](UCommonActivatableWidget* DeactivatedWidget)
	{
		// Use a small delay to ensure the widget is fully removed from the stack
		// before evaluating the input context
		if (UWorld* World = DeactivatedWidget->GetWorld())
		{
			FTimerHandle TimerHandle;
			World->GetTimerManager().SetTimer(TimerHandle, []()
			{
				UUIManager::UpdateInputContextForUIState();
			}, 0.1f, false);
		}
	});
}

// Fill out your copyright notice in the Description page of Project Settings.


#include "BlueprintLibraries/UIHelperLibrary.h"
#include "CoreMinimal.h"
#include "AdvancedSteamFriendsLibrary.h"
#include "Settings/CustomUISettings.h"
#include "UI/BaseMainLayerUI.h"
#include "UI/ErrorWidgetBase.h"
#include "UI/UIManager.h"
#include "Core/StormEscapeGameInstance.h"
#include "Kismet/GameplayStatics.h"


FString UUIHelperLibrary::GetProjectVersion()
{
	FString ProjectVersion;
	GConfig->GetString(
		TEXT("/Script/EngineSettings.GeneralProjectSettings"),
		TEXT("ProjectVersion"),
		ProjectVersion,
		GGameIni
	);

	return ProjectVersion;
}

bool UUIHelperLibrary::MatchesRegex(const FString& Input, const FString& Pattern)
{
	const FRegexPattern RegexPattern(Pattern);
	FRegexMatcher Matcher(RegexPattern, Input);
	return Matcher.FindNext(); 
}

void UUIHelperLibrary::ShowErrorPopup(const FString& ErrorHead, const FString& ErrorBody, UObject* WorldContextObject)
{
	if (!WorldContextObject) return;
	
	// Access the error widget class from the settings
	if (UCustomUISettings* CustomUISettings = GetMutableDefault<UCustomUISettings>())
	{
		TSubclassOf<UErrorWidgetBase> ErrorWidgetClass = CustomUISettings->ErrorWidgetClass;
		if (!ErrorWidgetClass) return;

		// Use the UIManager to spawn the widget
		UCommonActivatableWidget* SpawnedWidget = UUIManager::AddWidgetToStack(
			WorldContextObject,
			ErrorWidgetClass,
			EWidgetLayer::Modal,
			EInputModeType::UIOnly,
			true,
			false
		);
		
		// Initialize the error message
		if (UErrorWidgetBase* ErrorWidget = Cast<UErrorWidgetBase>(SpawnedWidget))
		{
			ErrorWidget->InitErrorWidget(ErrorHead, ErrorBody);
		}
	}
}


void UUIHelperLibrary::OpenSteamOverlay(UObject* WorldContextObject)
{
	if (SteamFriends())
	{
		SteamFriends()->ActivateGameOverlay("Friends");
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Steam Overlay is not available."));
	}
}

int UUIHelperLibrary::GetPlayerCount(UObject* WorldContextObject)
{
	if (!IsValid(WorldContextObject)) return 0;

	UStormEscapeGameInstance* GI = Cast<UStormEscapeGameInstance>(WorldContextObject->GetWorld()->GetGameInstance());
	if (IsValid(GI))
	{
		return GI->GetCurrentLobbyData().CurrentPlayers;
	}

	return UGameplayStatics::GetNumPlayerStates(WorldContextObject);
}

const FNamedOnlineSession* UUIHelperLibrary::GetSession(UObject* WorldContextObject)
{
	if (!IsValid(WorldContextObject)) return nullptr;

	IOnlineSessionPtr sessionInterface = Online::GetSessionInterface(WorldContextObject->GetWorld());
	return sessionInterface.IsValid() ? sessionInterface->GetNamedSession("StormEscape") : nullptr;
}

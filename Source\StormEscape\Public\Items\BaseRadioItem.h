// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Items/BaseDeviceItem.h"
#include "Interfaces/VoiceEmitterInterface.h"
#include "Voice/RadioSubsystem.h"
#include "Components/SynthComponent.h"
#include "BaseRadioItem.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnRadioStartedTransmitting);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnRadioStoppedTransmitting);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnRadioStartedReceiving);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnRadioStoppedReceiving);

UCLASS()
class STORMESCAPE_API ABaseRadioItem : public ABaseDeviceItem, public IVoiceEmitterInterface
{
	GENERATED_BODY()
	
public:
	ABaseRadioItem();

	UOdinSynthComponent* CreateVoiceSynthComponent_Implementation(const FVoiceRoomData& roomData, int64 peerID, UOdinPlaybackMedia* media) override;

	UOdinAudioGeneratorLoopbackComponent* CreateVoiceLoopbackComponent_Implementation(const FVoiceRoomData& roomData, int64 peerID) override;

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	UPROPERTY(BlueprintAssignable)
	FOnRadioStartedTransmitting OnStartedTransmittingDelegate;
	UPROPERTY(BlueprintAssignable)
	FOnRadioStoppedTransmitting OnStoppedTransmittingDelegate;
	UPROPERTY(BlueprintAssignable)
	FOnRadioStartedReceiving OnStartedReceivingDelegate;
	UPROPERTY(BlueprintAssignable)
	FOnRadioStoppedReceiving OnStoppedReceivingDelegate;

protected:
	virtual void BeginPlay() override;

	virtual void BindCustomInputs(UEnhancedInputComponent* inputComponent) override;

	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

	void Activate_Implementation() override;

	void Deactivate_Implementation() override;

	void Talk();

	void StopTalking();

	UFUNCTION()
	virtual void OnRep_ReceivingIdUpdate(int64 oldID);

	UPROPERTY(ReplicatedUsing = OnRep_ReceivingIdUpdate, BlueprintReadOnly)
	int64 receivingID;

	void ToggleSynth(int64 peerID, bool bOn);

	UPROPERTY(EditAnywhere, Category = "Properties")
	TSubclassOf<UOdinSynthComponent> synthClass;

	UPROPERTY(EditAnywhere, Category = "Properties")
	TSubclassOf<UOdinAudioGeneratorLoopbackComponent> loopbackClass;

	UPROPERTY(BlueprintReadWrite)
	TMap<int64, USynthComponent*> peerToSynthMap;

	UFUNCTION()
	virtual void OnRep_TransmittingIdUpdate(int64 oldID);

	UPROPERTY(ReplicatedUsing = OnRep_TransmittingIdUpdate, BlueprintReadWrite)
	int64 transmittingID;

	URadioSubsystem* radioSystem;

	UPROPERTY(BlueprintReadOnly)
	TArray<int64> signalIDs;

	UFUNCTION()
	void OnSignalChanged(int64 peerID);

	void Listen();

	void StopListening();

	UFUNCTION()
	void OpenMicrophone();

	UFUNCTION()
	void CloseMicrophone();

	UPROPERTY(EditAnywhere, Category = "Properties|Input", meta = (DisplayAfter = "customInputMapping"))
	UInputAction* microphoneAction;

	// The amount of time the radio remains silent after a transmission received ends.
	UPROPERTY(EditAnywhere, Category = "Properties|Output", meta = (DisplayAfter = "microphoneAction"))
	float tailCooldown;

	bool bIsMicOpen;

	void StartTailCooldown();

	void StopTailCooldown();

	UPROPERTY(Replicated, BlueprintReadWrite)
	bool bOnTailCooldown;

private:
	UFUNCTION(Server, BlueprintCallable, Reliable)
	void Server_Talk();

	UFUNCTION(Server, BlueprintCallable, Reliable)
	void Server_StopTalking();

	UFUNCTION(Server, BlueprintCallable, Reliable)
	void Server_Listen();

	UFUNCTION(Server, BlueprintCallable, Reliable)
	void Server_StopListening();

	void SetReceivingID(int64 peerID);

	void SetTransmittingID(int64 peerID);

	void UpdateTailCooldown(float deltaSeconds);

	float tailCooldownTime;

	FTimerHandle cooldownTimer;

	int64 localPeerID;

	UOdinAudioCapture* localCapture;

protected:
	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnTransmissionStarted")
	void K2_OnTransmissionStarted();

	UFUNCTION(BlueprintImplementableEvent, DisplayName = "TransmissionEnded")
	void K2_OnTransmissionEnded();

	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnSignalStarted")
	void K2_OnSignalStarted();

	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnSignalEnded")
	void K2_OnSignalEnded();

	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnSpeakerChanged")
	void K2_OnSpeakerChanged(bool bHasSpeaker, int64 peerID);
};

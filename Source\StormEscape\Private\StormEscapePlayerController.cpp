// Fill out your copyright notice in the Description page of Project Settings.


#include "StormEscapePlayerController.h"

#include "BaseFirstPersonCharacter.h"
#include "EnhancedInputComponent.h"
#include "BlueprintLibraries/UIHelperLibrary.h"
#include "Components/GameplayVoiceChatComponent.h"
#include "Core/Lobby/LobbyGameMode.h"
#include "GameFramework/Character.h"
#include "GameFramework/PawnMovementComponent.h"
#include "Settings/CustomUISettings.h"
#include "UI/BaseMainLayerUI.h"
#include "UI/UIManager.h"

AStormEscapePlayerController::AStormEscapePlayerController()
{
	VoiceChatComp = CreateDefaultSubobject<UGameplayVoiceChatComponent>(TEXT("Gameplay VoiceChat"));
	AddOwnedComponent(VoiceChatComp);
}

void AStormEscapePlayerController::ShowErrorPopup_Implementation(const FString& ErrorHead, const FString& ErrorBody)
{
	IControllerInterface::ShowErrorPopup_Implementation(ErrorHead, ErrorBody);
	
	if (IsLocalController())
	{
		// Show the error popup
		UUIHelperLibrary::ShowErrorPopup(ErrorHead, ErrorBody, this);
	}else
	{
		// Call the function on the client
		ClientShowErrorPopup(ErrorHead, ErrorBody);
	}
}

void AStormEscapePlayerController::KickToMainMenu_Implementation(const FString& Reason)
{
	IControllerInterface::KickToMainMenu_Implementation(Reason);

	if (IsLocalController())
	{
		if (UStormEscapeGameInstance* GI = UStormEscapeGameInstance::GetActive_GameInstance(this))
		{
			GI->KickAndReturnToMenu(Reason);
		}
		
	}else
	{
		// Call the function on the client
		Client_KickToMainMenu(Reason);
	}
	
}

void AStormEscapePlayerController::ClientShowLoadingScreen_Implementation()
{
	//get the game instance and toggle the loading screen
	if (UStormEscapeGameInstance* GI = UStormEscapeGameInstance::GetActive_GameInstance(this))
	{
		GI->BeginLoadingScreen("Loading...");
	}
}

void AStormEscapePlayerController::ClientShowErrorPopup_Implementation(const FString& ErrorHead,
                                                                       const FString& ErrorBody)
{
	UUIHelperLibrary::ShowErrorPopup(ErrorHead, ErrorBody, this);
}

void AStormEscapePlayerController::BeginPlay()
{
	Super::BeginPlay();

	GameHUD = Cast<AStormEscapeHUD>(GetHUD());

	// get the game mode and cast it to lobby game mode and bind to on match started
	if (ALobbyGameMode* GameMode = Cast<ALobbyGameMode>(GetWorld()->GetAuthGameMode()))
	{
		GameMode->OnMatchStarted.AddDynamic(this, &AStormEscapePlayerController::ClientShowLoadingScreen);
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Game mode is not of type ALobbyGameMode"));
	}

	if (IsLocalController())
	{
		//Initialize the UI manager
		if (MainLayerUIClass)
		{
			MainLayerUI = CreateWidget<UBaseMainLayerUI>(this, MainLayerUIClass);
			MainLayerUI->AddToViewport();
			UUIManager::Initialize(this, MainLayerUI);
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("Main layer UI class is not set"));
		}

		//Get the Pause Menu class and Cache it
		if (const UCustomUISettings* UISettings = GetDefault<UCustomUISettings>())
		{
			PauseMenuClass = UISettings->PauseMenuWidgetClass;
		}
	}
	
}

void AStormEscapePlayerController::SetupInputComponent()
{
	Super::SetupInputComponent();

	if (UEnhancedInputComponent* EnhancedInput = Cast<UEnhancedInputComponent>(InputComponent))
	{
		if (PauseAction)
		{
			EnhancedInput->BindAction(PauseAction, ETriggerEvent::Started, this, &AStormEscapePlayerController::TogglePauseMenu);
		}
	}
}

void AStormEscapePlayerController::Client_KickToMainMenu_Implementation(const FString& String)
{
	if (UStormEscapeGameInstance* GI = UStormEscapeGameInstance::GetActive_GameInstance(this))
	{
		GI->KickAndReturnToMenu(String);
	}
}

void AStormEscapePlayerController::TogglePauseMenu()
{
	UUIManager::AddWidgetToStack(this, PauseMenuClass, EWidgetLayer::Menu, EInputModeType::UIOnly, true);
}

void AStormEscapePlayerController::NotifyTornadoHit(AActor* tornado)
{
	if (GetCharacter()->GetClass()->ImplementsInterface(UDamageInterface::StaticClass()))
	{
		IDamageInterface::Execute_Kill(GetCharacter(), tornado);
	}
}

// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Data/WorkbenchSpawnSettings.h"
#include "Components/ItemHookComponent.h"
#include "Workbench.generated.h"

USTRUCT(BlueprintType)
struct FWorkbenchZoneData : public FItemBenchData
{
	GENERATED_BODY()

public:
	FWorkbenchZoneData() : itemCount(0) {}
	FWorkbenchZoneData(const FItemBenchData& inData) : itemCount(0)
	{
		itemSize = inData.itemSize;
		spawnLimit = inData.spawnLimit;
		spawnChance = inData.spawnChance;
	}

	bool SpawnRandomItem(FItemSpawnData& outData, TSet<EItemCategory> priorities = {});

	const TSet<EItemCategory> GetCategories() const { return categoryPool; }

	void RegisterItemData(FName itemName, FItemSpawnData data);

	void RegisterSlot(UItemHookComponent* slot) { slots.AddUnique(slot); }

protected:
	float GetItemChance(const FItemSpawnData& data, TSet<EItemCategory> priorities);

	TMap<FName, FItemSpawnData> itemPool;

	TSet<EItemCategory> categoryPool;

	TArray<UItemHookComponent*> slots;

	int itemCount;
};

UCLASS()
class STORMESCAPE_API AWorkbench : public AActor
{
	GENERATED_BODY()
	
public:	
	// Sets default values for this actor's properties
	AWorkbench();

	void GenerateItems(int numPlayers);

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	void GenerateItem(TSet<EItemSize> pool);

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	bool bSpawnItems;

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	UWorkbenchSpawnSettings* settings;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UStaticMeshComponent* mesh;

private:
	void Assemble();

	EItemSize GetRandomSize(TSet<EItemSize> pool) const;

	TMap<EItemSize, FWorkbenchZoneData> zoneMap;

	TMap<EItemCategory, int> categoryCountMap;
};

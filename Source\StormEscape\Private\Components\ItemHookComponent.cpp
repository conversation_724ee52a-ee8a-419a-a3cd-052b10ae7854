// Fill out your copyright notice in the Description page of Project Settings.


#include "Components/ItemHookComponent.h"
#include "Net/UnrealNetwork.h"

UItemHookComponent::UItemHookComponent()
{
	PrimaryComponentTick.bCanEverTick = false;
	SetIsReplicatedByDefault(true);
	bWantsInitializeComponent = true;
	itemSize = EItemSize::Small;
	bConstrainItem = false;
}

void UItemHookComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(UItemHookComponent, owningItem);
}

void UItemHookComponent::SpawnItem(const FItemSpawnData& data)
{
	if (IsValid(data.itemClass))
	{
		FActorSpawnParameters SpawnInfo;
		owningItem = GetWorld()->SpawnActor<ABaseItem>(data.itemClass, GetComponentLocation(), GetComponentRotation() + data.customRotation, SpawnInfo);
		OnRep_ItemUpdate(nullptr);
		owningItem->OnCarrierChangedDelegate.AddUniqueDynamic(this, &UItemHookComponent::OnItemPickedUp);
	}
}

void UItemHookComponent::OnRep_ItemUpdate(ABaseItem* oldItem)
{
	if (!bConstrainItem)
	{
		return;
	}

	ToggleHook(oldItem, false);
	ToggleHook(owningItem, true);
}

void UItemHookComponent::OnItemPickedUp(AActor* carrier)
{
	owningItem->OnCarrierChangedDelegate.RemoveAll(this);
	ABaseItem* oldItem = owningItem;
	owningItem = nullptr;
	OnRep_ItemUpdate(oldItem);
}

void UItemHookComponent::ToggleHook(ABaseItem* item, bool bHooked)
{
	if (IsValid(item))
	{
		UStaticMeshComponent* itemMesh = IInteractionInterface::Execute_GetItemMesh(item);
		if (IsValid(itemMesh))
		{
			//itemMesh->SetSimulatePhysics();
			itemMesh->SetPhysicsAngularVelocityInDegrees(FVector::ZeroVector);
			itemMesh->SetPhysicsLinearVelocity(FVector::ZeroVector);
			itemMesh->SetEnableGravity(!bHooked);
		}
	}
}

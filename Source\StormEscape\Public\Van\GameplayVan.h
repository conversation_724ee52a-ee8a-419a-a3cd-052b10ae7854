#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Interfaces/RepairInterface.h"
#include "GameplayVan.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPartRemoved, EVehiclePart, vehiclePart);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPartInstalled, EVehiclePart, vehiclePart);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnJackInstalled);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnTireInstalled);

UCLASS()
class STORMESCAPE_API AGameplayVan : public AActor
{
	GENERATED_BODY()
	
public:	
	AGameplayVan();

	virtual void Tick(float DeltaTime) override;

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	UPROPERTY(BlueprintCallable)
	FOnPartRemoved OnPartRemovedDelegate;

	UPROPERTY(BlueprintCallable)
	FOnPartInstalled OnPartInstalledDelegate;

	UPROPERTY(BlueprintCallable)
	FOnJackInstalled OnJackInstalledDelegate;

	UPROPERTY(BlueprintCallable)
	FOnTireInstalled OnTireInstalledDelagate;

	UPROPERTY(Replicated, BlueprintReadWrite, Category = "Properties|Conditions")
	bool bJackInstalled;

	UPROPERTY(Replicated, BlueprintReadOnly, Category = "Properties|Conditions")
	bool bHasHealtyBattery;

	UPROPERTY(Replicated, BlueprintReadOnly, Category = "Properties|Conditions")
	bool bHasHealtyFuses;

	UPROPERTY(Replicated, EditAnywhere, BlueprintReadOnly, Category = "Properties|Conditions")
	bool bDoorsOpen;

	UPROPERTY(Replicated, BlueprintReadWrite, Category = "Properties|Conditions")
	bool bVanStarted;

	UFUNCTION(BlueprintImplementableEvent)
	void HandleJackInserted(FName spotTag);

	UFUNCTION()
	void HandleJackRemoved();

	UFUNCTION(BlueprintImplementableEvent)
	void LiftVan(AActor* item, float liftProgress, float liftDuration);

	UFUNCTION(BlueprintCallable, BlueprintPure)
	bool IsVanRepaired();

	UFUNCTION(BlueprintCallable, Category = "Fuel")
	void FillFuelTank(float fuelAmount);

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Fuel")
	bool IsFuelTankFull();

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Fuel")
	void UpdateFuelWidget(float value);

	UFUNCTION(BlueprintCallable, Category = "Anti-Freeze")
	void FillFreezeTank(float freezeAmount);

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Anti-Freeze")
	bool IsFreezeTankFull();

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Anti-Freeze")
	void UpdateFreezeMaterial(float fillProgress);

	UFUNCTION(BlueprintCallable, Category = "Oil")
	void FillOilTank(float oilAmount);

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Oil")
	bool IsOilTankFull();

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Oil")
	void UpdateOilMaterial(float fillProgress);

	UFUNCTION(BlueprintCallable, Category = "Engine")
	void HandleEngineLidOpen(const bool bOpen);

protected:
	virtual void BeginPlay() override;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UStaticMeshComponent* vanMesh;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UChildActorComponent* engineLid;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UChildActorComponent* liftButton;

	//Tires

	UPROPERTY(VisibleAnywhere)
	UChildActorComponent* frontWheelSpotR;

	UPROPERTY(VisibleAnywhere)
	UChildActorComponent* frontWheelSpotL;

	UPROPERTY(VisibleAnywhere)
	UChildActorComponent* backWheelSpotR;

	UPROPERTY(VisibleAnywhere)
	UChildActorComponent* backWheelSpotL;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UChildActorComponent* frontWheelR;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UChildActorComponent* frontWheelL;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UChildActorComponent* backWheelR;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UChildActorComponent* backWheelL;

	UPROPERTY(Replicated, EditAnywhere, BlueprintReadWrite)
	FRotator rotationOffset;

	UPROPERTY(Replicated, EditAnywhere, BlueprintReadOnly, Category = "Properties|Tires")
	int32 numberOfDamagedTires;

	// Fuel

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UChildActorComponent* fuelCap;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Fuel")
	float maxFuel;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Fuel")
	float minFuelToStart;

	UPROPERTY(Replicated, BlueprintReadWrite, Category = "Properties|Fuel")
	float fuel;

	//Engine

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UChildActorComponent* oilCap;

	UPROPERTY(Replicated, BlueprintReadOnly, Category = "Properties|Conditions")
	bool bEngineLidOpen;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UStaticMeshComponent* engineMesh;

	// AntiFreeze

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UChildActorComponent* coolantCap;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Anti-Freeze")
	float maxAntiFreeze;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Anti-Freeze")
	float minAntiFreezeToStart;

	UPROPERTY(Replicated, BlueprintReadWrite, Category = "Properties|Anti-Freeze")
	float antiFreeze;

	// Oil

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Oil")
	float maxOil;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Oil")
	float minOilToStart;

	UPROPERTY(Replicated, BlueprintReadWrite, Category = "Properties|Oil")
	float oil;

	// Battery

	UPROPERTY(VisibleAnywhere)
	UChildActorComponent* batterySpot;

	UPROPERTY(VisibleAnywhere)
	UChildActorComponent* battery;

	// Fuses

	UPROPERTY(VisibleAnywhere)
	UChildActorComponent* fuseBox;

	UPROPERTY(VisibleAnywhere)
	UChildActorComponent* fuses;

	UFUNCTION(BlueprintImplementableEvent)
	void EnableInteraction();

protected:
	UFUNCTION(BlueprintImplementableEvent)
	void ToggleDoorsOpen(const bool bShouldOpen);

private:
	void InitializeComponents();

	UFUNCTION()
	void HandlePartDamaged(EVehiclePart partDamaged);

	UFUNCTION()
	void HandlePartRemoved(EVehiclePart partRemoved);

	UFUNCTION()
	void HandlePartInstalled(EVehiclePart partInstalled);

	UFUNCTION()
	void HandleTireInstalled();

	UFUNCTION()
	void HandleLiftButtonPressed(const bool bShouldOpen);
};

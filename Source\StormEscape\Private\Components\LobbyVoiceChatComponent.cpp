﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "Components/LobbyVoiceChatComponent.h"
#include "OdinSynthComponent.h"
#include "OdinFunctionLibrary.h"
#include "OdinAudioCapture.h"
#include "Kismet/KismetGuidLibrary.h"
#include "AudioCaptureBlueprintLibrary.h"
#include "OdinTokenGenerator.h"
#include "Core/StormEscapeGameInstance.h"

ULobbyVoiceChatComponent::ULobbyVoiceChatComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
	SetIsReplicatedByDefault(true);
	
}

void ULobbyVoiceChatComponent::BeginPlay()
{
	Super::BeginPlay();

	APlayerController* PC = Cast<APlayerController>(GetOwner());

	if (PC->IsLocalController())
	{
		FTimerHandle VoiceChatInitHandle;
		// Try calling after a short delay to give time for the player stat to be initialized.
		GetWorld()->GetTimerManager().SetTimer(VoiceChatInitHandle, this, &ULobbyVoiceChatComponent::TryStartVoiceChat, 1.0f, false);
	}
	
}

void ULobbyVoiceChatComponent::TryStartVoiceChat()
{
	APlayerController* PC = Cast<APlayerController>(GetOwner());
	if (PC && PC->GetPlayerState<APlayerState>())
	{
		ConnectToVoiceChat();
	}
	else
	{
		FTimerHandle VoiceChatInitHandle;
		// Retry again after a short delay
		GetWorld()->GetTimerManager().SetTimer(VoiceChatInitHandle, this, &ULobbyVoiceChatComponent::TryStartVoiceChat, 0.5f, false);
	}
}

void ULobbyVoiceChatComponent::ConnectToVoiceChat()
{
	tokenGenerator = UOdinTokenGenerator::ConstructTokenGenerator(this, "AasLZzlm4bYupwpBZvZSQsuzN0IJwJPyRwArDxW2vvyO");
	
	UStormEscapeGameInstance* GI = UStormEscapeGameInstance::GetActive_GameInstance(this);

	if (!GI)
	{
		UE_LOG(LogTemp, Error, TEXT("ULobbyVoiceChatComponent: GameInstance is null."));
		return;
	}

	FString RoomId = GI->GetCurrentLobbyData().LobbyName;

	APlayerController* PC = Cast<APlayerController>(GetOwner());
	if (!PC || !PC->GetPlayerState<APlayerState>())
	{
		UE_LOG(LogTemp, Error, TEXT("ULobbyVoiceChatComponent: PlayerController or PlayerState is null."));
		return;
	}
	FString UserId = PC->GetPlayerState<APlayerState>()->GetPlayerName();


	UE_LOG(LogTemp, Warning, TEXT("Odin from GI RoomId: %s"), *RoomId);
	UE_LOG(LogTemp, Warning, TEXT("Odin from GI UserId: %s"), *UserId);


	roomToken = tokenGenerator->GenerateRoomToken(RoomId, UserId, EOdinTokenAudience::Default);
	UE_LOG(LogTemp, Warning, TEXT("Room Token Generated: %s"), *roomToken);


	room = UOdinRoom::ConstructRoom(this, AudioSettings);

	room->onPeerJoined.AddUniqueDynamic(this, &ULobbyVoiceChatComponent::OnOdinPeerJoined);
	room->onMediaAdded.AddUniqueDynamic(this, &ULobbyVoiceChatComponent::OnOthersMediaAdded);
	OnRoomJoinSuccess.BindUFunction(this, TEXT("OnJoinedOdinRoom"));
	OnRoomJoinError.BindUFunction(this, TEXT("OnOdinErrorHandler"));
	
	TArray<uint8> userData = {};

	UOdinRoomJoin* Action = UOdinRoomJoin::JoinRoom(this, room, TEXT("https://gateway.odin.4players.io"), roomToken, userData, FVector(0, 0, 0), OnRoomJoinError, OnRoomJoinSuccess);
	Action->Activate();
}

void ULobbyVoiceChatComponent::OnOthersMediaAdded(int64 peerId, UOdinPlaybackMedia* media, UOdinJsonObject* properties,
	UOdinRoom* odinRoom)
{
	//We create a Synth Component and add it to our player controller
	//and assign that new player media to it so we can start listening for them.
	
	UActorComponent* comp = GetOwner()->AddComponentByClass(UOdinSynthComponent::StaticClass(), false, FTransform::Identity, false);
	UOdinSynthComponent* synth = StaticCast<UOdinSynthComponent*>(comp);

	IdSynthMap.Add(peerId, synth);
	
	synth->Odin_AssignSynthToMedia(media);
	synth->Activate();

	UE_LOG(LogTemp, Warning, TEXT("Odin Synth Added"));
}

void ULobbyVoiceChatComponent::OnOthersMediaRemoved(int64 peerId, UOdinPlaybackMedia* media, UOdinRoom* odinRoom)
{
	if (TObjectPtr<UOdinSynthComponent>* OdinSynthComponentPtr = IdSynthMap.Find(peerId))
	{
		if (OdinSynthComponentPtr->Get())
		{
			OdinSynthComponentPtr->Get()->DestroyComponent();
		}
		IdSynthMap.Remove(peerId);

		UE_LOG(LogTemp, Log, TEXT("LobbyVoiceComp: another player Media Removed"));
	}
}

void ULobbyVoiceChatComponent::OnOdinPeerJoined(int64 peerId, FString userId, const TArray<uint8>& userData,
	UOdinRoom* odinRoom)
{
	UE_LOG(LogTemp, Log, TEXT("Odin Peer Joined - UserId: %s | PeerId: %lld"), *userId, peerId);
}

void ULobbyVoiceChatComponent::OnOdinPeerLeft(int64 peerId, UOdinRoom* odinRoom)
{
	UE_LOG(LogTemp, Log, TEXT("Odin Peer Left - PeerId: %lld"), peerId);
}

void ULobbyVoiceChatComponent::OnJoinedOdinRoom(FString RoomId, const TArray<uint8>& RoomUserData,
	FString Customer, int64 OwnPeerId, FString OwnUserId)
{
	//Upon joining an odin Room we should start Capturing our own voice and Store it in a Media
	//and Add that Media to the Room so other players can Register us.
	
	UE_LOG(LogTemp, Warning, TEXT("Joined Room"));

	capture = UOdinFunctionLibrary::CreateOdinAudioCapture(this);
	UAudioGenerator* captureAsGenerator = (UAudioGenerator*)capture;

	auto media = UOdinFunctionLibrary::Odin_CreateMedia(captureAsGenerator);
	
	OnAddMediaError.BindUFunction(this, TEXT("OnOdinErrorHandler"));

	UOdinRoomAddMedia* Action = UOdinRoomAddMedia::AddMedia(this, room, media, OnAddMediaError, OnAddMediaSuccess);
	Action->Activate();

	capture->StartCapturingAudio();
}

void ULobbyVoiceChatComponent::OnOdinErrorHandler(int64 ErrorCode)
{
	FString ErrorString = UOdinFunctionLibrary::FormatError(ErrorCode, true);
	UE_LOG(LogTemp, Error, TEXT("%s"), *ErrorString);
}


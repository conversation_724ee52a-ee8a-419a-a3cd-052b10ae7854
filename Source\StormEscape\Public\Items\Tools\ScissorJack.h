#pragma once

#include "CoreMinimal.h"
#include "Items/Tools/BaseTool.h"
#include "Van/Tire.h"
#include "ScissorJack.generated.h"

class UCameraComponent;
class UInteractComponent;
class ATire;

UCLASS()
class STORMESCAPE_API AScissorJack : public ABaseTool
{
	GENERATED_BODY()
	
public:
	AScissorJack();

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	void Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	virtual void Complete_Implementation() override;

	FORCEINLINE bool RequiresTool_Implementation() override { return bRequieresToolBox; }

	UPROPERTY(Replicated)
	bool bIsPlaced;

protected:
	UPROPERTY(EditDefaultsOnly, Category = "Properties|Position")
	FVector placementOffset;

	UPROPERTY(EditAnywhere, Category = "Properties")
	float timeToLiftVan;

	EVehiclePart vehiclePart;

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void ApplyTransparency(bool bShouldApply);

private:
	ATire* raisedTire = nullptr;

	UPROPERTY(Replicated)
	bool bCanBeRemoved;

	UPROPERTY(Replicated)
	bool bRequieresToolBox;

	void TraceVehicleParts(UInteractComponent* interactComponent);

	void InstallJack(ATire* tire);

	UFUNCTION()
	void HandleTireInstalled();
};

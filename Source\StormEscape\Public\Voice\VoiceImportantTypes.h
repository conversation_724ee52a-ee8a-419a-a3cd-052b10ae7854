#pragma once

#include "OdinRoom.h"
#include "OdinAudioCapture.h"
#include "VoiceImportantTypes.generated.h"

UENUM(BlueprintType)
enum class EVoiceRoomType : uint8
{
	Proximity = 0	UMETA(DisplayName = "Proximity"),
	Radio = 1		UMETA(DisplayName = "Radio")
};

USTRUCT(BlueprintType)
struct FVoiceRoomData
{
	GENERATED_BODY()

public:
	FVoiceRoomData() : roomID(FString()), audioCapture(nullptr), roomType(EVoiceRoomType::Proximity), attenuation(nullptr) {}
	FVoiceRoomData(FString inRoomID, UOdinAudioCapture* inAudioCapture, EVoiceRoomType inRoomType, USoundAttenuation* inAttenuation) : 
		roomID(inRoomID), audioCapture(inAudioCapture), roomType(inRoomType), attenuation(inAttenuation) {}

	FString roomID;

	UOdinAudioCapture* audioCapture;

	EVoiceRoomType roomType;

	USoundAttenuation* attenuation;
};

#include "Van/EngineLid.h"
#include "Van/GameplayVan.h"

AEngineLid::AEngineLid()
{
	bRequiresToolBox = false;
	vehiclePart = EVehiclePart::None;
	actionDuration = 2.f;
	bIsOpen = false;
	progressType = EProgressType::Activation;
}

void AEngineLid::Complete_Implementation()
{
	Super::Complete_Implementation();

	if (vanRef)
	{
		ToggleOpenLid();
		bIsOpen = !bIsOpen;
		vanRef->HandleEngineLidOpen(bIsOpen);
	}
}

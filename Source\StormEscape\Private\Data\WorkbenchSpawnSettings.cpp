// Fill out your copyright notice in the Description page of Project Settings.


#include "Data/WorkbenchSpawnSettings.h"
#include "UtilityTypes.h"

UWorkbenchSpawnSettings::UWorkbenchSpawnSettings() 
{
	itemCount.Add(1, FIntegerInterval(2, 4));
	itemCount.Add(2, FIntegerInterval(2, 4));
	itemCount.Add(3, FIntegerInterval(5, 8));
	itemCount.Add(4, FIntegerInterval(5, 8));
	itemCount.Add(5, FIntegerInterval(9, 12));
	itemCount.Add(6, FIntegerInterval(9, 12));
	itemCount.Add(7, FIntegerInterval(13, 14));
	itemCount.Add(8, FIntegerInterval(13, 14));

	pegboardSettings = FItemBenchData(EItemSize::Small, 8, 0.57f);
	tableSettings = FItemBenchData(EItemSize::Medium, 3, 0.29f);
	underTableSettings = FItemBenchData(EItemSize::Large, 2, 0.14f);
}



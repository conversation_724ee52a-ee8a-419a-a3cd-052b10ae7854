# StormEscape
StormEscape
Storm Survival is a semi-realistic, first-person survival game set in a 1990s rural America, where players must fix a broken-down vehicle and escape an increasingly deadly tornado. The game blends horror, chaos, and goofy physics with non-lethal survival mechanics and multiplayer co-op for up to 8 players.

## Technical Overview
- Engine: Unreal 5.5
- Language: C++ (no visual scripting)
- Target Platform: Windows 10/11, built with DirectX12 (fallback to DX11)
- Network: Peer-to-peer/random lobby multiplayer via Steam
- Perspective: First-person
- Graphics: Realistic models, high-poly where needed, performance optimized (frustum culling, LODs, async loading)
- UI: Dynamic canvases for radar, health, stamina, and in-world shops
- Tools: 
- Multiplayer Support: Local and online co-op, guest account support

## Gameplay Features
Dynamic weather system with semi-randomized tornado progression
Real-time environmental destruction, NPC behavior, and survival mechanics
Objective-based missions based on vehicle damage (engine, tires, etc.)
Procedurally placed loot and tools in destructible, explorable environments
Unique blend of survival tension and comedic physics Expect to tackle systems like:

- Real-time storm simulation
- Environmental effects tied to audio/lighting
- Seamless open-world streaming
- Physics-based interactions
- First-person input with contextual interactions
- Steamworks multiplayer integration

There are just some of the many other challenges that will need to tackled during the duration of this game's development cycle.

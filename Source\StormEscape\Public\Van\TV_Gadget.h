﻿#pragma once

#include "CoreMinimal.h"
#include "Items/BaseItem.h"
#include "Blueprint/UserWidget.h"
#include "TV_Gadget.generated.h"

UCLASS()
class STORMESCAPE_API ATV_Gadget : public ABaseItem
{
	GENERATED_BODY()

public:
	// Activate the Screen
	virtual void Use_Implementation() override;

	UFUNCTION(Client, Reliable)
	void ClientShowWidget(TSubclassOf<UCommonActivatableWidget> WidgetToDisplay);

	UFUNCTION(BlueprintCallable, Category = "UI")
	void OnWidgetClosed(UCommonActivatableWidget* DestroyedWidget);

	UFUNCTION(BlueprintImplementableEvent, Category = "UI")
	void K2_OnWidgetClosed();

protected:
	// Widget class for the Lobby
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "UI")
	TSubclassOf<UCommonActivatableWidget> LobbyWidgetClass;

	// Widget class for the Main Menu
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "UI")
	TSubclassOf<UCommonActivatableWidget> MainMenuWidgetClass;
};

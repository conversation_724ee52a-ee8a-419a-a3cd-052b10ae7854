// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "ControllerInterface.generated.h"

// This class does not need to be modified.
UINTERFACE()
class UControllerInterface : public UInterface
{
	GENERATED_BODY()
};

/**
 * 
 */
class STORMESCAPE_API IControllerInterface
{
	GENERATED_BODY()

	// Add interface functions to this class. This is the class that will be inherited to implement this interface.
public:

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "UI")
	void KickToMainMenu(const FString& Reason);
	
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "UI")
	void ShowErrorPopup(const FString& ErrorHead, const FString& ErrorBody);
	
};

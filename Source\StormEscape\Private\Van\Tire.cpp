#include "Van/Tire.h"
#include "Van/GameplayVan.h"
#include "Net/UnrealNetwork.h"

ATire::ATire()
{
	vehiclePart = EVehiclePart::Tire;
	progressType = EProgressType::Persistent;
	actionDuration = 0.f;
	bIsRemovable = true;
}

void ATire::Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	if (bIsHeld)
	{
		Super::Interact_Implementation(interactor, interactComponent);
	}
	else
	{
		if (bCanBeRemoved || !bIsDamaged)
		{
			Super::Interact_Implementation(interactor, interactComponent);
		}
	}
}

bool ATire::HasJackInstalled_Implementation()
{
	if (vanRef)
	{
		return vanRef->bJackInstalled;
	}
	return false;
}
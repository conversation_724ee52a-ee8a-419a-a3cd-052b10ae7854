
#include "Items/BaseItem.h"
#include "Components/SphereComponent.h"
#include "Components/InteractComponent.h"
#include "Net/UnrealNetwork.h"

DEFINE_LOG_CATEGORY(LogItem)

ABaseItem::ABaseItem()
{
	PrimaryActorTick.bCanEverTick = false;

	bReplicates = true;
	//SetReplicates(true);
	SetReplicateMovement(true);

	itemMesh = CreateDefaultSubobject<UStaticMeshComponent>("ItemMesh");
	SetRootComponent(itemMesh);

	itemCollision = CreateDefaultSubobject<USphereComponent>("ItemCollision");
	itemCollision->SetupAttachment(itemMesh);

	//itemMesh->SetMassOverrideInKg(FName("None"), 25.f, true);
	itemMesh->BodyInstance.bOverrideMass = true;
	itemMesh->BodyInstance.SetMassOverride(25.f);


	bAutoActivate = false;
	bActivated = false;
	bIsFocused = false;
	actionDuration = 0.f;

	progress = 0.f;

	activationType = EActivationType::Hold;
	progressType = EProgressType::Activation;
}

void ABaseItem::BeginPlay()
{
	Super::BeginPlay();

	bActivated = false;
	bIsComplete = false;
	bIsFocused = false;
	progress = 0.f;

	if (bAutoActivate)
	{
		Activate();
	}

	if (bIsPickable && itemMesh)
	{
		itemMesh->SetSimulatePhysics(true);
		itemMesh->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
	}
}


void ABaseItem::TryUse_Implementation()
{
	if (CanUse())
	{
		Use();
		CleanUse();
	}
}

void ABaseItem::TryActivate_Implementation()
{
	if (CanActivate())
	{
		Activate();
	}
}

void ABaseItem::TryDeactivate_Implementation()
{
	if (bActivated)
	{
		Deactivate();
	}
}

bool ABaseItem::CanUse_Implementation() const
{
	return true;
}

bool ABaseItem::CanActivate_Implementation() const
{
	return progressType == EProgressType::None || (!bActivated && !bIsComplete);
}

void ABaseItem::Multicast_OnUsed_Implementation()
{
	K2_OnUsed();
}

void ABaseItem::OnRep_ActivatedUpdate()
{
	bActivated ? K2_OnActivated() : K2_OnDeactivated();
}

void ABaseItem::OnRep_IsHeldUpdate()
{
	bIsHeld ? K2_OnPickedUp() : K2_OnDropped();
}

void ABaseItem::OnRep_IsFocusedUpdate()
{
	bIsFocused ? K2_OnFocused() : K2_OnUnfocused();
}

void ABaseItem::OnRep_IsCompleteUpdate()
{
	bIsComplete ? K2_OnProgressCompleted() : K2_OnProgressReset();
}

void ABaseItem::OnRep_ProgressUpdate()
{
	K2_OnProgressChanged(progress, actionDuration);
	OnProgressChangedDelegate.Broadcast(this, progress, actionDuration);
}

void ABaseItem::SetActionDuration(float newDuration)
{
	actionDuration = newDuration;
	OnRep_ActionDurationChanged();
}

void ABaseItem::OnRep_ActionDurationChanged()
{
	if (IsValid(userComponent))
	{
		userComponent->NotifyProgress(this, progress, actionDuration);
		userComponent->NotifyItemSettingsUpdate(this);
	}
}

void ABaseItem::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(ABaseItem, userComponent);
	DOREPLIFETIME(ABaseItem, bIsPickable);
	DOREPLIFETIME(ABaseItem, bActivated);
	DOREPLIFETIME(ABaseItem, bIsHeld);
	DOREPLIFETIME(ABaseItem, bIsFocused);
	DOREPLIFETIME(ABaseItem, bIsComplete);
	DOREPLIFETIME(ABaseItem, progress);
	DOREPLIFETIME(ABaseItem, actionDuration);
}

void ABaseItem::Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	if (interactor && !bIsHeld && !GetOwner())
	{
		SetOwner(interactor); // Required for RPCs. Held items already have an owner.
	}

	if (interactComponent && CanFocus())
	{
		OnActionStartedDelegate.AddUniqueDynamic(interactComponent, &UInteractComponent::SetIsInteracting);
	}

	switch (activationType)
	{
	case EActivationType::Use:
		TryUse();
		break;

	case EActivationType::Toggle:
		bActivated ? TryDeactivate() : TryActivate();
		break;

	case EActivationType::Hold:
		if (!bActivated)
		{
			TryActivate();
		}
		break;
	}
}

void ABaseItem::Interrupt_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	if (bActivated && activationType == EActivationType::Hold)
	{
		TryDeactivate();
		interactComponent->NotifyItemSettingsUpdate(this);
		if (!bIsHeld && GetOwner() == interactor)
		{
			SetOwner(GetDefaultOwner()); // Clearing ownership so other interactors can receive Client RPCs. Held items keep their owner until dropped.
		}
	}
}

void ABaseItem::NotifyStartAction_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	if (interactComponent && CanFocus())
	{
		userComponent = interactComponent;
		OnProgressChangedDelegate.AddUniqueDynamic(interactComponent, &UInteractComponent::NotifyProgress);
		OnActionCompletedDelegate.AddUniqueDynamic(interactComponent, &UInteractComponent::StopInteraction);
	}
}

void ABaseItem::NotifyStopAction_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	if (interactComponent && CanFocus())
	{
		userComponent = nullptr;
		OnProgressChangedDelegate.RemoveAll(interactComponent);
		OnActionStartedDelegate.RemoveAll(interactComponent);
		OnActionCompletedDelegate.RemoveAll(interactComponent);
	}
}

void ABaseItem::Use_Implementation()
{
	Multicast_OnUsed();
}


void ABaseItem::CleanUse_Implementation()
{
	if (!bIsHeld)
	{
		SetOwner(GetDefaultOwner()); // Clearing ownership so other interactors can receive Client RPCs. Held items keep their owner until dropped.
	}
}

void ABaseItem::Activate_Implementation()
{
	bActivated = true;
	OnRep_ActivatedUpdate();

	FTimerDelegate updateDelegate = FTimerDelegate::CreateUObject(this, &ABaseItem::Update, updateFreq);
	GetWorld()->GetTimerManager().SetTimer(updateTimer, updateDelegate, updateFreq, true);

	if (progressType > EProgressType::None && ShouldProgress())
	{
		OnActionStartedDelegate.Broadcast();
		FTimerDelegate processDelegate = FTimerDelegate::CreateUObject(this, &ABaseItem::Process, updateFreq);
		GetWorld()->GetTimerManager().SetTimer(processTimer, processDelegate, updateFreq, true);
	}
	else if (actionDuration == 0.f && bActivatesOnce)
	{
		bIsComplete = true;
		OnRep_IsCompleteUpdate();
	}
}

void ABaseItem::Update_Implementation(float deltaSeconds) {}

void ABaseItem::Process_Implementation(float deltaSeconds)
{
	progress += deltaSeconds;
	OnRep_ProgressUpdate();

	if (progress >= actionDuration)
	{
		Complete();
	}
}

bool ABaseItem::ShouldProgress_Implementation()
{
	return !bIsComplete && actionDuration > 0.f;
}

void ABaseItem::Complete_Implementation()
{
	bIsComplete = true;
	OnRep_IsCompleteUpdate();
	OnActionCompletedDelegate.Broadcast();
	GetWorld()->GetTimerManager().ClearTimer(processTimer);

	if (!bActivatesOnce)
		RestartProgress();
}

void ABaseItem::RestartProgress_Implementation()
{
	progress = 0;
	OnRep_ProgressUpdate();

	if (bIsComplete)
	{
		bIsComplete = false;
		OnRep_IsCompleteUpdate();
	}
}

void ABaseItem::Deactivate_Implementation()
{
	bActivated = false;
	OnRep_ActivatedUpdate();

	if (progressType == EProgressType::Activation && ShouldProgress())
	{
		progress = 0.f;
		OnRep_ProgressUpdate();
	}

	GetWorld()->GetTimerManager().ClearTimer(processTimer);
}

void ABaseItem::NotifyPickUp_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	if (progressType > EProgressType::None)
	{
		OnProgressChangedDelegate.AddUniqueDynamic(interactComponent, &UInteractComponent::NotifyProgress);
		OnActionCompletedDelegate.AddUniqueDynamic(interactComponent, &UInteractComponent::StopInteraction);
	}

	SetOwner(interactor);
	OnCarrierChangedDelegate.Broadcast(interactor);
	userComponent = interactComponent;
	bIsHeld = true;
	OnRep_IsHeldUpdate();
}

void ABaseItem::NotifyDrop_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	if (progressType > EProgressType::None)
	{
		OnProgressChangedDelegate.RemoveAll(interactComponent);
		OnActionCompletedDelegate.RemoveAll(interactComponent);
	}

	SetOwner(GetDefaultOwner());
	OnCarrierChangedDelegate.Broadcast(nullptr);
	userComponent = nullptr;
	bIsHeld = false;
	OnRep_IsHeldUpdate();
}

void ABaseItem::NotifyFocus_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	bIsFocused = true;
	OnRep_IsFocusedUpdate();
}

void ABaseItem::NotifyUnfocus_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	bIsFocused = false;
	OnRep_IsFocusedUpdate();
}

bool ABaseItem::CanFocus_Implementation() const
{
	return !bIsPickable && !bIsComplete && progressType > EProgressType::None && actionDuration > 0.f;
}

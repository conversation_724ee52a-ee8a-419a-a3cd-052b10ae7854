﻿#include "UI/Lobby/PageSelectorWidget.h"

TArray<FPaginationEntry> UPageSelectorWidget::GeneratePagination(int32 CurrentPage, int32 TotalPages, int32 VisibleRange)
{
	TArray<FPaginationEntry> Result;

	if (TotalPages <= 1)
	{
		Result.Add(FPaginationEntry(1));
		return Result;
	}

	auto AddPage = [&](int32 PageNum)
	{
		Result.Add(FPaginationEntry(PageNum));
	};

	auto AddEllipsis = [&]()
	{
		Result.Add(FPaginationEntry());
	};

	// Always add first page
	AddPage(1);

	int32 StartPage = FMath::Max(2, CurrentPage - VisibleRange);
	int32 EndPage = FMath::Min(TotalPages - 1, CurrentPage + VisibleRange);

	if (StartPage > 2)
	{
		AddEllipsis();
	}

	for (int32 i = StartPage; i <= EndPage; ++i)
	{
		AddPage(i);
	}

	if (EndPage < TotalPages - 1)
	{
		AddEllipsis();
	}

	// Always add last page
	if (TotalPages > 1)
	{
		AddPage(TotalPages);
	}

	return Result;
}
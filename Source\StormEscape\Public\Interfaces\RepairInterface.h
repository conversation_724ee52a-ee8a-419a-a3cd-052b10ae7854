
#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "RepairInterface.generated.h"

UENUM(BlueprintType)
enum class EVehiclePart : uint8
{
	None UMETA(DisplayName = "None"),
	Tire UMETA(DisplayName = "Tire"),
	Fuel UMETA(DisplayName = "Fuel"),
	Oil UMETA(DisplayName = "Oil"),
	Anti_Freeze UMETA(DisplayName = "Anti-Freeze"),
	Battery UMETA(DisplayName = "Battery"),
	Fuses UMETA(DisplayName = "Fuses"),
	Scissor_Jack UMETA(DisplayName = "Scissor Jack")
};

UINTERFACE(MinimalAPI)
class URepairInterface : public UInterface
{
	GENERATED_BODY()
};

class STORMESCAPE_API IRepairInterface
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	bool IsSamePartType(EVehiclePart InVehiclePart);

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	bool HasJackInstalled();
};

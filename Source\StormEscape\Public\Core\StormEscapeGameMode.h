// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameModeBase.h"
#include "Weather/Tornado.h"
#include "StormEscapeGameMode.generated.h"

UCLASS()
class STORMESCAPE_API AStormEscapeGameMode : public AGameModeBase
{
	GENERATED_BODY()
	

protected:
	UPROPERTY(EditDefaultsOnly, Category="Weather Settings")
	TSubclassOf<ATornado> tornadoClass;

public:
	virtual void StartPlay() override;

	virtual void PostLogin(APlayerController* NewPlayer) override;

	virtual void Logout(AController* Exiting) override;

protected:
	UFUNCTION()
	void OnPlayerDeath(APlayerController* Player);

	virtual void BeginPlay() override;
};

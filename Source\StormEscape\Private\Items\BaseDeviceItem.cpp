// Fill out your copyright notice in the Description page of Project Settings.


#include "Items/BaseDeviceItem.h"
#include "Net/UnrealNetwork.h"
#include "EnhancedInputSubsystems.h"

ABaseDeviceItem::ABaseDeviceItem()
{
	activationType = EActivationType::Toggle;
	progressType = EProgressType::None;

	durability = 1.f;
	maxDurability = 1.f;
	activationCost = 0.f;
	consumePerSecond = 0.f;
}

void ABaseDeviceItem::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(ABaseDeviceItem, durability);
}

void ABaseDeviceItem::NotifyPickUp_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	Super::NotifyPickUp_Implementation(interactor, interactComponent);

	if (HasCustomInput())
	{
		Client_AddCustomInputMapping(interactor);
	}
	
}

void ABaseDeviceItem::NotifyDrop_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	if (HasCustomInput())
	{
		Client_RemoveCustomInputMapping(interactor);
	}

	Super::NotifyDrop_Implementation(interactor, interactComponent);
}

void ABaseDeviceItem::BeginPlay()
{
	Super::BeginPlay();

	durability = maxDurability;
}

bool ABaseDeviceItem::CanActivate_Implementation() const
{
	return Super::CanActivate_Implementation() && durability > activationCost;
}

void ABaseDeviceItem::Activate_Implementation()
{
	Super::Activate_Implementation();
	durability -= activationCost;
	OnRep_DurabilityUpdate();

	if (durability > 0.f && consumePerSecond > 0.f)
	{
		FTimerDelegate processDelegate = FTimerDelegate::CreateUObject(this, &ABaseDeviceItem::DecreaseDurability, updateFreq);
		GetWorld()->GetTimerManager().SetTimer(decreaseTimer, processDelegate, updateFreq, true);
	}
}

bool ABaseDeviceItem::ShouldProgress_Implementation()
{
	return Super::ShouldProgress_Implementation() && !IsDepleted();
}

void ABaseDeviceItem::DecreaseDurability_Implementation(float deltaSeconds)
{
	durability -= GetConsume() * deltaSeconds;
	if (durability <= 0.f)
	{
		durability = 0.f;
		Deactivate();
	}

	OnRep_DurabilityUpdate();
}

void ABaseDeviceItem::Deactivate_Implementation()
{
	Super::Deactivate_Implementation();

	GetWorld()->GetTimerManager().ClearTimer(decreaseTimer);
}

void ABaseDeviceItem::Client_AddCustomInputMapping_Implementation(AActor* interactor)
{
	AddCustomInputMapping(interactor);
}

void ABaseDeviceItem::AddCustomInputMapping(AActor* interactor)
{
	APawn* carrier = Cast<APawn>(interactor);
	APlayerController* playerController = Cast<APlayerController>(carrier->GetController());
	if (playerController)
	{
		if (UEnhancedInputLocalPlayerSubsystem* inputSystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(playerController->GetLocalPlayer()))
		{
			inputSystem->AddMappingContext(customInputMapping, 0);
		}
	}

	BindCustomInputs(Cast<UEnhancedInputComponent>(carrier->InputComponent));
}

void ABaseDeviceItem::Client_RemoveCustomInputMapping_Implementation(AActor* interactor)
{
	RemoveCustomInputMapping(interactor);
}

void ABaseDeviceItem::RemoveCustomInputMapping(AActor* interactor)
{
	APawn* carrier = Cast<APawn>(interactor);
	APlayerController* playerController = Cast<APlayerController>(carrier->GetController());
	if (playerController)
	{
		if (UEnhancedInputLocalPlayerSubsystem* inputSystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(playerController->GetLocalPlayer()))
		{
			inputSystem->RemoveMappingContext(customInputMapping);
		}
	}

	RemoveCustomInputs(Cast<UEnhancedInputComponent>(carrier->InputComponent));
}

void ABaseDeviceItem::RemoveCustomInputs(UEnhancedInputComponent* inputComponent)
{
	if (inputComponent)
	{
		for (FInputBindingHandle binding : customBindings)
		{
			inputComponent->RemoveBinding(binding);
		}
	}

	customBindings.Empty();
}

void ABaseDeviceItem::OnRep_DurabilityUpdate()
{
	FMath::IsNearlyZero(durability) ? K2_OnDepleted() : K2_OnDurabilityChanged(durability, maxDurability);
}

#pragma once

//#include "EngineMinimal.h"
//#include "Core.h"
//#include "OnlineSessionInterface.h"
//#include "OnlineSessionSettings.h"
//#include "OnlineDelegateMacros.h"
//#include "OnlineSubsystem.h"
//#include "OnlineSubsystemImpl.h"
//#include "OnlineSubsystemUtils.h"
//#include "OnlineSubsystemUtilsModule.h"
//#include "ModuleManager.h"
//#include "OnlineSubsystemUtilsClasses.h"
//#include "BlueprintDataDefinitions.h"


/*#include "VoiceEngineImpl.h"
#include "VoiceInterfaceImpl.h"
#include "Voice.h""
*/

// Found this in the steam controller, seems like a nice thought since steam is throwing errors
// Disable crazy warnings that claim that standard C library is "deprecated".
//#ifdef _MSC_VER
//#pragma warning(push)
//#pragma warning(disable:4996)
//#endif

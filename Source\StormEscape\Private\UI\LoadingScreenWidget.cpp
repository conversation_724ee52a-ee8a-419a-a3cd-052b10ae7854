// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/LoadingScreenWidget.h"

bool ULoadingScreenWidget::Initialize()
{
	bIsDisplaying = false;
	bIsAnimating = false;
	lastRequest = ELoadingRequest::None;
	return Super::Initialize();
}

void ULoadingScreenWidget::Toggle(bool bVisible)
{
	// Check if the loading screen is already in the desired state and if the next loading screen is blocked
	if (bBlockNextLoadingScreen && bVisible)
	{
		bBlockNextLoadingScreen = false;
		return;
	}

	if (bIsAnimating)
	{
		lastRequest = bVisible ? ELoadingRequest::Open : ELoadingRequest::Close;
		return;
	}

	if (bIsDisplaying != bVisible)
	{
		GetWorld()->GetTimerManager().ClearTimer(exitTimer);
		bIsAnimating = true;
		bVisible ? OnEnter() : OnExit();
	}

	bBlockNextLoadingScreen = false;
}

void ULoadingScreenWidget::NotifyAnimationFinished(bool bOpen)
{
	bIsDisplaying = bOpen;
	if (bIsDisplaying)
	{
		FTimerDelegate exitDelegate = FTimerDelegate::CreateUObject(this, &ULoadingScreenWidget::OnExpired);
		GetWorld()->GetTimerManager().SetTimer(exitTimer, exitDelegate, minDuration, false);
		OnStartedDelegate.Broadcast();
		return;
	}

	if (lastRequest == ELoadingRequest::Open)
	{
		lastRequest = ELoadingRequest::None;
		OnEnter();
		return;
	}

	bIsAnimating = false;
	OnEndedDelegate.Broadcast();
}

void ULoadingScreenWidget::OnExpired()
{
	if (bIsDisplaying && lastRequest == ELoadingRequest::Close)
	{
		lastRequest = ELoadingRequest::None;
		OnExit();
		return;
	}
	
	bIsAnimating = false;
}

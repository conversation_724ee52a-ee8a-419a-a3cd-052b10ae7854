#include "Van/BatterySpot.h"
#include "Van/BatteryTerminal.h"
#include "Van/Battery.h"

ABatterySpot::ABatterySpot()
{
	redTerminal = CreateDefaultSubobject<UChildActorComponent>("RedTerminal");
	redTerminal->SetupAttachment(spotMesh);

	blackTerminal = CreateDefaultSubobject<UChildActorComponent>("BlackTerminal");
	blackTerminal->SetupAttachment(spotMesh);

	vehiclePart = EVehiclePart::Battery;

	bRedTerminalInstalled = true;
	bBlackTerminalInstalled = true;
}

void ABatterySpot::BeginPlay()
{
	Super::BeginPlay();

	if (redTerminal->GetChildActor())
	{
		if (ABatteryTerminal* terminal = Cast<ABatteryTerminal>(redTerminal->GetChildActor()))
		{
			redTerminalRef = terminal;
			terminal->OnTerminalInteractedDelegate.AddDynamic(this, &ABatterySpot::HandleRedTerminalInteraction);
		}
		redTransform = redTerminal->GetRelativeTransform();
	}

	if (blackTerminal->GetChildActor())
	{
		if (ABatteryTerminal* terminal = Cast<ABatteryTerminal>(blackTerminal->GetChildActor()))
		{
			blackTerminalRef = terminal;
			terminal->OnTerminalInteractedDelegate.AddDynamic(this, &ABatterySpot::HandleBlackTerminalInteraction);
		}
		blackTransform = blackTerminal->GetRelativeTransform();
	}

}

void ABatterySpot::SetBatteryReference(ABattery* battery)
{
	batteryRef = battery;

	if (batteryRef && redTerminalRef && blackTerminalRef)
	{
		redTerminalRef->bHasBatteryInstalled = true;
		blackTerminalRef->bHasBatteryInstalled = true;
	}
	else if (!batteryRef && redTerminalRef && blackTerminalRef)
	{
		redTerminalRef->bHasBatteryInstalled = false;
		blackTerminalRef->bHasBatteryInstalled = false;
	}
}

void ABatterySpot::HandleRedTerminalInteraction(bool bLoose)
{
	bRedTerminalInstalled = !bLoose;

	if (bLoose)
		Multicast_ToggleTerminal(redTerminal, redLooseTransform);
	else
		Multicast_ToggleTerminal(redTerminal, redTransform);

	CheckTerminals();
}

void ABatterySpot::HandleBlackTerminalInteraction(bool bLoose)
{
	bBlackTerminalInstalled = !bLoose;

	if (bLoose)
		Multicast_ToggleTerminal(blackTerminal, blackLooseTransform);
	else
		Multicast_ToggleTerminal(blackTerminal, blackTransform);

	CheckTerminals();
}

void ABatterySpot::CheckTerminals()
{
	if (!batteryRef)
		return;

	if (bRedTerminalInstalled && bBlackTerminalInstalled)
	{
		batteryRef->HandleTerminalsInstalled();
	}
	else if (!bRedTerminalInstalled && !bBlackTerminalInstalled)
	{
		batteryRef->bCanBeRemoved = true;
	}
	else
	{
		batteryRef->bCanBeRemoved = false;
	}
}

void ABatterySpot::Multicast_ToggleTerminal_Implementation(UChildActorComponent* terminal, FTransform transform)
{
	terminal->SetRelativeLocationAndRotation(transform.GetLocation(), transform.GetRotation());
}
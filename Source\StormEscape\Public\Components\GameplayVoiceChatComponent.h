﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "OdinRoom.h"
#include "Components/ActorComponent.h"
#include "Core/StormEscapeGameInstance.h"
#include "Voice/VoiceImportantTypes.h"
#include "Voice/RadioSubsystem.h"
#include "GameplayVoiceChatComponent.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogGameVoice, Log, All);

class UOdinSynthComponent;
class UOdinTokenGenerator;
class UOdinAudioCapture;

UCLASS(Blueprintable ,ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class STORMESCAPE_API UGameplayVoiceChatComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	// Sets default values for this component's properties
	UGameplayVoiceChatComponent();

	UFUNCTION(BlueprintCallable)
	void TryStartVoiceChat(FGuid PlayerId);
	void JoinRoom(FString RoomId, EVoiceRoomType Type, USoundAttenuation* DefaultAttenuation);

	UFUNCTION(BlueprintCallable)
	UOdinRoom* GetOdinRoom(EVoiceRoomType RoomType);

	UFUNCTION(BlueprintCallable)
	bool MutePeer(FString UserName, bool bMute);
	
	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite)
	USoundAttenuation* ProximitySoundAttenuation;

	UPROPERTY(EditDefaultsOnly, BlueprintReadWrite)
	USoundAttenuation* RadioSoundAttenuation;


	UPROPERTY(BlueprintReadOnly)
	TMap<int64, FString> PeerToUserMap;

protected:
	// Called when the game starts
	virtual void BeginPlay() override;

	void ConnectToVoiceChat(FGuid PlayerId);

	void AddMediaToVoiceEmitter(AActor* actor, int64 peerId, UOdinPlaybackMedia* media, UOdinRoom* room);

	/* Local Variables */
	TMap<int64,ACharacter*> PeerToCharacterMap;

	UPROPERTY()
	UOdinTokenGenerator* TokenGenerator;

	UPROPERTY()
	TArray<UOdinRoom*> OdinRoom;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FOdinApmSettings AudioSettings;

	
	/* Delegates and CallBacks */
	FOdinRoomJoinError OnRoomJoinError;
	FOdinRoomJoinSuccess OnRoomJoinSuccess;
	FOdinRoomAddMediaError OnAddMediaError;
	FOdinRoomAddMediaSuccess OnAddMediaSuccess;

	UFUNCTION()
	virtual void OnOthersMediaAdded(int64 peerId, UOdinPlaybackMedia* media, UOdinJsonObject* properties, UOdinRoom* Room);
	UFUNCTION()
	virtual void OnOthersMediaRemoved(int64 peerId,UOdinPlaybackMedia* media, UOdinRoom* Room);
	UFUNCTION()
	virtual void OnOdinPeerJoined(int64 peerId, FString userId,const TArray<uint8>& userData, UOdinRoom* Room);
	UFUNCTION()
	virtual void OnOdinPeerLeft(int64 peerId, UOdinRoom* Room);
	UFUNCTION()
	virtual void OnJoinedOdinRoom(FString RoomId, const TArray<uint8>& RoomUserData, FString Customer, int64 OwnPeerId, FString OwnUserId);
	UFUNCTION()
	virtual void OnOdinErrorHandler(int64 ErrorCode);

private:
	UStormEscapeGameInstance* GameInstance;
	APlayerController* PlayerController;
	FGuid OwnerPlayerId;
	TMap<UOdinRoom*, FVoiceRoomData> RoomsToDataMap;
	TMap<FString , UOdinRoom*> RoomIdToRoomMap;
	TMap<int64, TMap<UOdinRoom*, UOdinSynthComponent*>> PeerToRoomSynthMap;

	URadioSubsystem* radioSystem;
};
﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonButtonBase.h"
#include "OptionSelector.generated.h"

class UTextBlock;
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnOptionChanged, int32, NewIndex, FText, NewValue);


/**
 * 
 */
UCLASS()
class STORMESCAPE_API UOptionSelector : public UCommonButtonBase
{
	GENERATED_BODY()

public:

	virtual void NativePreConstruct() override;
	
	UFUNCTION(BlueprintCallable)
	void ChangeSelection(int32 NewIndex);
	
	UFUNCTION(BlueprintCallable)
	void ChangeOptions(TArray<FText> NewOptions, int32 NewDefaultSelection);
	
	UFUNCTION(BlueprintPure)
	TArray<FText> GetOptionsArray() const {return Options;}
	
	// Default Selected Value on Construction
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Option Selector" ,meta = (ExposeOnSpawn = "true"))
	int32 DefaultSelection = 0;

	// Current index selected
	UPROPERTY(BlueprintReadOnly, Category = "Option Selector")
	int32 CurrentIndex = 0;

	// Event delegate when option changes
	UPROPERTY(BlueprintAssignable, Category = "Option Selector")
	FOnOptionChanged OnOptionChanged;

private:
	// Internal list of possible options
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Option Selector" ,meta = (ExposeOnSpawn = "true" , AllowPrivateAccess = "true"))
	TArray<FText> Options;
	
};


#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "InteractComponent.generated.h"


DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnInteractionFocusItemChanged, AActor*, item);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnInteractionHeldItemChanged, AActor*, item);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnInteractionItemSettingsChanged, AActor*, item);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnInteractionProgressChanged, AActor*, item, float, progress);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnInteractionSpeedModifierChanged, float, percent);

class UCameraComponent;
class UCharacterAttributesComponent;
class UPhysicsConstraintComponent;

UCLASS( ClassGroup=(Custom), meta=(BlueprintSpawnableComponent) )
class STORMESCAPE_API UInteractComponent : public UActorComponent
{
	GENERATED_BODY()

public:	
	UInteractComponent();

	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	UPROPERTY(BlueprintAssignable)
	FOnInteractionFocusItemChanged OnFocusItemChangedDelegate;

	UPROPERTY(BlueprintAssignable)
	FOnInteractionHeldItemChanged OnHeldItemChangedDelegate;

	UPROPERTY(BlueprintAssignable)
	FOnInteractionItemSettingsChanged OnItemSettingsChangedDelegate;

	UPROPERTY(BlueprintAssignable)
	FOnInteractionProgressChanged OnInteractionProgressChangedDelegate;

	UPROPERTY(BlueprintAssignable)
	FOnInteractionSpeedModifierChanged OnSpeedModifierChangedDelegate;

	UPROPERTY(Replicated, BlueprintReadOnly)
	AActor* heldItem;

	UPROPERTY(Replicated)
	bool bIsInteracting;

	UFUNCTION(BlueprintCallable)
	void InitializeInteractionComponent(UCameraComponent* InPlayerCamera, UPrimitiveComponent* InHeldItemSlot, UPhysicsConstraintComponent* InPhysicsConstraint, bool bApplyDefaultSettings);

	UFUNCTION(BlueprintCallable, BlueprintPure)
	FORCEINLINE bool CanInteract() { return hitItem && !bIsInteracting; }

	UFUNCTION(BlueprintCallable, BlueprintPure)
	bool IsHoldingItem();

	UFUNCTION()
	void SetIsInteracting();

	UFUNCTION(BlueprintCallable)
	bool GetTraceResult(float reach, FHitResult& outResult);

	UFUNCTION(BlueprintCallable)
	void StartInteraction(bool bWithTool = false);

	UFUNCTION(BlueprintCallable)
	void StopInteraction();

	UFUNCTION(BlueprintCallable)
	void PerformGrab(AActor* item);

	UFUNCTION(BlueprintPure)
	FVector GetHoldPosition() const { return heldItemSlot->GetComponentLocation(); }

	UFUNCTION(BlueprintCallable)
	void DropItem(const bool bApplyImpulse = true);

	UFUNCTION(BlueprintCallable)
	void ExecuteInteractEvent(AActor* item);

	UFUNCTION(BlueprintCallable)
	void ExecuteInterruptEvent(AActor* item);

	UFUNCTION(BlueprintCallable)
	void NotifyProgress(AActor* item, float progressTime, float duration);

	UFUNCTION(BlueprintCallable)
	void NotifyItemSettingsUpdate(AActor* item);

	UFUNCTION(BlueprintCallable)
	void AddSpeedModifier(AActor* source, float percent);

	UFUNCTION(BlueprintCallable)
	void RemoveSpeedModifier(AActor* source);

	UFUNCTION(BlueprintCallable)
	float GetSpeedModifier() const;

protected:
	virtual void BeginPlay() override;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Attributes | Interaction")
	float interactionDistance;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Attributes | Interaction")
	float interactionRadius;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Attributes | Interaction")
	float minThrowSrength;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Attributes | Interaction")
	float maxThrowSrength;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Attributes | Interaction", meta = (ClampMin = "0", UIMin = "0", ClampMax = "1", UIMax = "1"))
	float maxSpeedReduction;

private:
	UCameraComponent* playerCamera;

	UPrimitiveComponent* heldItemSlot;

	UPhysicsConstraintComponent* grabConstraint;

	void SetHitItem(AActor* item);

	UFUNCTION()
	void OnRep_HitItemUpdate(AActor* oldItem);

	UPROPERTY(ReplicatedUsing = OnRep_HitItemUpdate)
	AActor* hitItem;

	FTimerHandle interactionHandle;

	UFUNCTION(Server, Reliable)
	void Server_AddSpeedModifier(AActor* source, float percent);

	UFUNCTION(Server, Reliable)
	void Server_RemoveSpeedModifier(AActor* source);

	TMap<AActor*, float> sourceToSpeedModifierMap;

	void UpdateSpeedModifier();

	UFUNCTION()
	void OnRep_SpeedModifierUpdate();

	UPROPERTY(ReplicatedUsing = OnRep_SpeedModifierUpdate)
	float speedModifier;

	void ApplyDefaultPhysicsSettings();

	void TraceInteractables();

	void CheckForInteractables(AActor* hitActor);

	UFUNCTION(Server, Reliable)
	void Server_CheckForInteractables(AActor* item);

	void ClearInteractables();

	UFUNCTION(Server, Reliable)
	void Server_ClearInteractables();

	UFUNCTION(Server, Reliable)
	void Server_SetIsInteracting();

	UFUNCTION(Server, Reliable)
	void Server_StartInteraction(bool bWithTool);

	UFUNCTION(Client, Reliable)
	void Client_UpdateInteractionBar(AActor* item, float InInteractionTime, float InActionDuration);

	UFUNCTION(Client, Reliable)
	void Client_ApplyInteractionSettings(AActor* item);

	UFUNCTION(Server, Reliable)
	void Server_StopInteraction();

	UFUNCTION(Server, Reliable)
	void Server_Grab(AActor* item);

	UFUNCTION(NetMulticast, Reliable)
	void Multicast_Grab(AActor* item, UStaticMeshComponent* itemMesh, FVector_NetQuantize impulse);

	UFUNCTION(Server, Reliable)
	void Server_Grab_Body(AActor* otherPlayer);

	UFUNCTION(NetMulticast, Reliable)
	void Multicast_Grab_Body(USkeletalMeshComponent* bodyMesh, FVector_NetQuantize impulse);

	UFUNCTION(Server, Reliable)
	void Server_DropItem(const bool bApplyImpulse);

	UFUNCTION(NetMulticast, Reliable)
	void Multicast_DropItem(UStaticMeshComponent* itemMesh, FVector_NetQuantize impulse);

	UFUNCTION(NetMulticast, Reliable)
	void Multicast_DropItemSimple(UStaticMeshComponent* itemMesh);

	UFUNCTION(Server, Reliable)
	void Server_DropBody();

	UFUNCTION(NetMulticast, Reliable)
	void Multicast_DropBody(USkeletalMeshComponent* bodyMesh, FVector_NetQuantize impulse);
};

// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Items/BaseRadioItem.h"
#include "EnhancedInputComponent.h"
#include "WalkieTalkieItem.generated.h"

/**
 * 
 */
UCLASS()
class STORMESCAPE_API AWalkieTalkieItem : public ABaseRadioItem
{
	GENERATED_BODY()

public:
	AWalkieTalkieItem();

	void NotifyDrop_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

protected:
	virtual float GetConsume() const override;

};

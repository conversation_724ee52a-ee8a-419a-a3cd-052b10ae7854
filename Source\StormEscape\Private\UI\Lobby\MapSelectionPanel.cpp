﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/Lobby/MapSelectionPanel.h"

#include "Core/Lobby/LobbyGameState.h"

void UMapSelectionPanel::NativeConstruct()
{
	Super::NativeConstruct();
	
	if (ALobbyGameState* GS = GetWorld()->GetGameState<ALobbyGameState>())
	{
		if (!GS->OnMapChanged.IsAlreadyBound(this, &UMapSelectionPanel::OnCurrentMapChanged))
		{
			GS->OnMapChanged.AddDynamic(this, &UMapSelectionPanel::OnCurrentMapChanged);
			OnCurrentMapChanged(GS->CurrentPlayMap);
		}
	}
}

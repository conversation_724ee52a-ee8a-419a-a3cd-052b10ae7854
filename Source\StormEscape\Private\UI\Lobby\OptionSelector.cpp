﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/Lobby/OptionSelector.h"

#include "Components/TextBlock.h"

void UOptionSelector::NativePreConstruct()
{
	Super::NativePreConstruct();
	
	ChangeSelection(DefaultSelection);
}

void UOptionSelector::ChangeSelection(int32 NewIndex)
{
	if (Options.Num() == 0) return;
	CurrentIndex = NewIndex;
	CurrentIndex = FMath::Clamp(CurrentIndex, 0, Options.Num() - 1);
	OnOptionChanged.Broadcast(CurrentIndex, Options[CurrentIndex]);
}

void UOptionSelector::ChangeOptions(TArray<FText> NewOptions,int32 NewDefaultSelection)
{
	Options = NewOptions;
	DefaultSelection = NewDefaultSelection;
	ChangeSelection(DefaultSelection);
}

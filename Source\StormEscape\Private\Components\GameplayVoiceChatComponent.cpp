﻿// Fill out your copyright notice in the Description page of Project Settings.

#include "Components/GameplayVoiceChatComponent.h"

#include "BaseFirstPersonCharacter.h"
#include "OdinAudioCapture.h"
#include "OdinFunctionLibrary.h"
#include "OdinSynthComponent.h"
#include "OdinTokenGenerator.h"
#include "Core/StormEscapeGameInstance.h"
#include "GameFramework/Character.h"

DEFINE_LOG_CATEGORY(LogGameVoice)

UGameplayVoiceChatComponent::UGameplayVoiceChatComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
}

void UGameplayVoiceChatComponent::BeginPlay()
{
	Super::BeginPlay();

	GameInstance = UStormEscapeGameInstance::GetActive_GameInstance(this);
	PlayerController = Cast<APlayerController>(GetOwner());
	radioSystem = GetWorld()->GetSubsystem<URadioSubsystem>();
}

void UGameplayVoiceChatComponent::TryStartVoiceChat(FGuid PlayerId)
{
	if (!GetOwner())
	{
		FTimerHandle VoiceChatInitHandle;
		FTimerDelegate RetryDelegate;
		RetryDelegate.BindUFunction(this, FName("TryStartVoiceChat"), PlayerId);

		GetWorld()->GetTimerManager().SetTimer(VoiceChatInitHandle, RetryDelegate, 1.0f, false);
		return;
	}

	if (PlayerController && PlayerController->GetPlayerState<APlayerState>() && GameInstance)
	{
		ConnectToVoiceChat(PlayerId);
	}
	else
	{
		FTimerHandle VoiceChatInitHandle;
		FTimerDelegate RetryDelegate;
		RetryDelegate.BindUFunction(this, FName("TryStartVoiceChat"), PlayerId);

		GetWorld()->GetTimerManager().SetTimer(VoiceChatInitHandle, RetryDelegate, 1.0f, false);
	}
}

void UGameplayVoiceChatComponent::ConnectToVoiceChat(FGuid PlayerId)
{
	TokenGenerator = UOdinTokenGenerator::ConstructTokenGenerator(this, "AasLZzlm4bYupwpBZvZSQsuzN0IJwJPyRwArDxW2vvyO");

	OwnerPlayerId = PlayerId;

	FString RoomId = GameInstance->GetCurrentLobbyData().LobbyName;
	JoinRoom(RoomId + "_Proximity", EVoiceRoomType::Proximity, ProximitySoundAttenuation);
	JoinRoom(RoomId + "_Radio", EVoiceRoomType::Radio, RadioSoundAttenuation);
}

void UGameplayVoiceChatComponent::JoinRoom(FString RoomId, EVoiceRoomType Type, USoundAttenuation* DefaultAttenuation)
{
	FString UserId = PlayerController->GetPlayerState<APlayerState>()->GetPlayerName();
	UOdinAudioCapture* audioCapture = UOdinFunctionLibrary::CreateOdinAudioCapture(this);
	audioCapture->StartCapturingAudio();

	FString RoomToken = TokenGenerator->GenerateRoomToken(RoomId, UserId, EOdinTokenAudience::Default);

	OdinRoom.Add(UOdinRoom::ConstructRoom(this, AudioSettings));

	RoomsToDataMap.Add(OdinRoom.Last(), FVoiceRoomData(RoomId, audioCapture, Type, DefaultAttenuation));
	RoomIdToRoomMap.Add(RoomId, OdinRoom.Last());

	OdinRoom.Last()->onPeerJoined.AddUniqueDynamic(this, &UGameplayVoiceChatComponent::OnOdinPeerJoined);
	OdinRoom.Last()->onMediaAdded.AddUniqueDynamic(this, &UGameplayVoiceChatComponent::OnOthersMediaAdded);
	OnRoomJoinSuccess.BindUFunction(this, TEXT("OnJoinedOdinRoom"));
	OnRoomJoinError.BindUFunction(this, TEXT("OnOdinErrorHandler"));

	auto json = UOdinJsonObject::ConstructJsonObject(this);
	json->SetStringField(TEXT("PlayerId"), *OwnerPlayerId.ToString());
	TArray<uint8> userData = json->EncodeJsonBytes();

	UOdinRoomJoin* Action = UOdinRoomJoin::JoinRoom(this, OdinRoom.Last(), TEXT("https://gateway.odin.4players.io"), RoomToken, userData, FVector(0, 0, 0), OnRoomJoinError, OnRoomJoinSuccess);
	Action->Activate();
}

UOdinRoom* UGameplayVoiceChatComponent::GetOdinRoom(EVoiceRoomType RoomType)
{
	//Find the odin room assosicated with the room type using RoomsToDataMap
	for (auto room : RoomsToDataMap)
	{
		if (room.Value.roomType == RoomType)
		{
			return room.Key;
		}
	}
	return nullptr;
}

bool UGameplayVoiceChatComponent::MutePeer(FString UserName,bool bMute)
{
	for (auto User : PeerToUserMap)
	{
		if (User.Value == UserName)
		{
			if (PeerToRoomSynthMap.Contains(User.Key))
			{
				for (auto Synths : PeerToRoomSynthMap[User.Key])
				{
					if (bMute)
					{
						Synths.Value->Stop();
					}
					else
					{
						Synths.Value->Start();
					}
					return true;
				}
			}
		}
	}
	
	return false;
}

void UGameplayVoiceChatComponent::OnJoinedOdinRoom(FString RoomId, const TArray<uint8>& RoomUserData, FString Customer,
                                                   int64 OwnPeerId, FString OwnUserId)
{
	UE_LOG(LogGameVoice, Display, TEXT("%s: successfully joined room '%s'"), *OwnUserId, *RoomId, OwnPeerId);

	OnAddMediaError.BindUFunction(this, TEXT("OnOdinErrorHandler"));

	const FVoiceRoomData& roomData = RoomsToDataMap[RoomIdToRoomMap[RoomId]];
	UAudioGenerator* captureAsGenerator = (UAudioGenerator*)roomData.audioCapture;
	if (!IsValid(captureAsGenerator))
	{
		UE_LOG(LogGameVoice, Error, TEXT("Invalid audio capture for user '%s'"), *OwnUserId);
		return;
	}

	UOdinCaptureMedia* media = UOdinFunctionLibrary::Odin_CreateMedia(captureAsGenerator);
	UOdinRoomAddMedia* Action = UOdinRoomAddMedia::AddMedia(this, RoomIdToRoomMap[RoomId], media, OnAddMediaError, OnAddMediaSuccess);
	Action->Activate();

	if (roomData.roomType == EVoiceRoomType::Radio && radioSystem)
	{
		radioSystem->RegisterLocalSpeaker(PlayerController->GetCharacter(), OwnPeerId, roomData.audioCapture);
		UE_LOG(LogGameVoice, Display, TEXT("Adding loopback for user '%s' in room '%s'..."), *OwnUserId, *RoomId);

		TArray<AActor*> receivers = radioSystem->GetReceivers();
		UE_LOG(LogGameVoice, Display, TEXT("%d radio receivers found"), receivers.Num());
		for (AActor* receiver : receivers)
		{
			IVoiceEmitterInterface::Execute_CreateVoiceLoopbackComponent(receiver, roomData, OwnPeerId);
		}
	}
}

void UGameplayVoiceChatComponent::OnOdinPeerJoined(int64 peerId, FString userId, const TArray<uint8>& userData,
	UOdinRoom* Room)
{
	auto json = UOdinJsonObject::ConstructJsonObjectFromBytes(this, userData);
	FString guidString = json->GetStringField(TEXT("PlayerId"));

	FGuid guid = FGuid();
	if (FGuid::Parse(guidString, guid))
	{
		ACharacter* character = GameInstance->FindCharacterByGuid(guid);
		PeerToCharacterMap.Add(peerId, character);
		PeerToUserMap.FindOrAdd(peerId) = userId;

		if (RoomsToDataMap[Room].roomType == EVoiceRoomType::Radio && radioSystem)
		{
			radioSystem->RegisterSpeaker(character, peerId);
		}

		UE_LOG(LogGameVoice, Display, TEXT("User '%s' joined room '%s'"), *userId, **RoomIdToRoomMap.FindKey(Room));
	}
}

void UGameplayVoiceChatComponent::OnOdinPeerLeft(int64 peerId, UOdinRoom* Room)
{
	PeerToCharacterMap.Remove(peerId);
	if (PeerToRoomSynthMap.Contains(peerId)) {
		for (auto& Pair : PeerToRoomSynthMap[peerId]) {
			if (Pair.Value)
				Pair.Value->DestroyComponent();
		}
		PeerToRoomSynthMap.Remove(peerId);
	}

}

void UGameplayVoiceChatComponent::OnOthersMediaAdded(int64 peerId, UOdinPlaybackMedia* media,
	UOdinJsonObject* properties, UOdinRoom* room)
{

	switch (RoomsToDataMap[room].roomType)
	{
		case EVoiceRoomType::Proximity:
		{
			AddMediaToVoiceEmitter(*PeerToCharacterMap.Find(peerId), peerId, media, room);
			break;
		}

		case EVoiceRoomType::Radio:
		{
			if (!radioSystem)
			{
				UE_LOG(LogGameVoice, Warning, TEXT("Radio subsystem not found"));
				return;
			}

			TArray<AActor*> receivers = radioSystem->GetReceivers();
			UE_LOG(LogGameVoice, Display, TEXT("%d radio receivers found"), receivers.Num());
			for (AActor* receiver : receivers)
			{
				AddMediaToVoiceEmitter(receiver, peerId, media, room);
			}
			break;
		}
	}
}

void UGameplayVoiceChatComponent::AddMediaToVoiceEmitter(AActor* actor, int64 peerId, UOdinPlaybackMedia* media, UOdinRoom* room)
{
	if (!actor->IsValidLowLevel())
	{
		UE_LOG(LogGameVoice, Error, TEXT("Invalid media emitter: NULL ACTOR"));
		return;
	}

	if (!actor->GetClass()->ImplementsInterface(UVoiceEmitterInterface::StaticClass()))
	{
		UE_LOG(LogGameVoice, Error, TEXT("Invalid media emitter: %s"), *actor->GetName());
		return;
	}

	if (!room->IsValidLowLevel() || !RoomsToDataMap.Contains(room))
	{
		UE_LOG(LogGameVoice, Error, TEXT("Invalid room"));
		return;
	}

	if (!media->IsValidLowLevel())
	{
		UE_LOG(LogGameVoice, Error, TEXT("Invalid media"));
		return;
	}

	UOdinSynthComponent* synth = IVoiceEmitterInterface::Execute_CreateVoiceSynthComponent(actor, RoomsToDataMap[room], peerId, media);
	if (!IsValid(synth))
	{
		UE_LOG(LogGameVoice, Error, TEXT("Invalid synth created"));
		return;
	}

	PeerToRoomSynthMap.FindOrAdd(peerId).Add(room, synth);
	UE_LOG(LogGameVoice, Display, TEXT("Media added to actor '%s'"), *actor->GetName());
}

void UGameplayVoiceChatComponent::OnOthersMediaRemoved(int64 peerId, UOdinPlaybackMedia* media, UOdinRoom* Room)
{
	// Optional: implement if needed
}

void UGameplayVoiceChatComponent::OnOdinErrorHandler(int64 ErrorCode)
{
	FString errorString = UOdinFunctionLibrary::FormatError(ErrorCode, true);
	UE_LOG(LogGameVoice, Error, TEXT("%s"), *errorString);
}

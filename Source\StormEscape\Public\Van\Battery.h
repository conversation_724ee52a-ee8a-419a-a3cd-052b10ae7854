#pragma once

#include "CoreMinimal.h"
#include "Items/BaseVehiclePart.h"
#include "Battery.generated.h"

UCLASS()
class STORMESCAPE_API ABattery : public ABaseVehiclePart
{
	GENERATED_BODY()
	
public:
	ABattery();

	void Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;
	void HandleTerminalsInstalled();
	bool bCanBeRemoved;

protected:
	virtual void BeginPlay() override;

	virtual void InsertPart() override;

	virtual void RemovePart() override;
};

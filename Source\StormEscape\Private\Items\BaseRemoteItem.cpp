// Fill out your copyright notice in the Description page of Project Settings.


#include "Items/BaseRemoteItem.h"
#include "Components/InteractComponent.h"
#include "Net/UnrealNetwork.h"

ABaseRemoteItem::ABaseRemoteItem()
{
	cableSocket = CreateDefaultSubobject<UArrowComponent>("CablePlacement");
	cableSocket->SetupAttachment(GetRootComponent());

	bIsPickable = true;
	bAutoActivate = true;
	activationType = EActivationType::None;

	range = 200.f;
	pullStrength = 6000.f;
}

void ABaseRemoteItem::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(ABaseRemoteItem, targetItem);
}

void ABaseRemoteItem::NotifyPickUp_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	Super::NotifyPickUp_Implementation(interactor, interactComponent);
	ownerInteractComponent = interactComponent;

	if (targetItem && targetItem->HasCustomInput())
	{
		Client_AddInputToTarget(interactor);
	}

	if (targetConstraint)
	{
		FTimerDelegate cableDelegate = FTimerDelegate::CreateUObject(this, &ABaseRemoteItem::CheckCableLimit);
		GetWorld()->GetTimerManager().SetTimer(cableTimer, cableDelegate, updateFreq, true);
		targetConstraint->BreakConstraint();
	}
}

void ABaseRemoteItem::NotifyDrop_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	if (targetItem && targetItem->HasCustomInput())
	{
		Client_RemoveInputFromTarget(interactor);
	}

	Super::NotifyDrop_Implementation(interactor, interactComponent);
	ownerInteractComponent = nullptr;

	if (targetConstraint)
	{
		GetWorld()->GetTimerManager().ClearTimer(cableTimer);
		GetWorld()->GetTimerManager().SetTimerForNextTick(FTimerDelegate::CreateWeakLambda(this, [this]()
			{
				targetConstraint->InitComponentConstraint();
				targetConstraint->ConstraintInstance.Pos2 = targetAttachPoint;
			}));
	}
}

void ABaseRemoteItem::SetTarget(ABaseDeviceItem* target, float maxDistance, UCableComponent* cable, UPhysicsConstraintComponent* constraint, float pull)
{
	targetItem = target;
	OnRep_TargetItemUpdate();

	range = maxDistance;
	rangeSquared = FMath::Square(range);
	pullStrength = pull;

	if (constraint)
	{
		targetConstraint = constraint;
		constraint->ConstraintActor2 = this;
		constraint->InitComponentConstraint();
		targetAttachPoint = targetConstraint->ConstraintInstance.Pos2;
	}
}

void ABaseRemoteItem::ToggleRemote(bool bEnabled)
{
	bIsPickable = bEnabled;
	bRemoteEnabled = bEnabled;
}

void ABaseRemoteItem::Client_AddInputToTarget_Implementation(AActor* interactor)
{
	targetItem->AddCustomInputMapping(interactor);
}

void ABaseRemoteItem::Client_RemoveInputFromTarget_Implementation(AActor* interactor)
{
	targetItem->RemoveCustomInputMapping(interactor);
}

void ABaseRemoteItem::OnRep_TargetItemUpdate()
{
	K2_OnTargetChanged(targetItem);

	if (UCableComponent* cable = targetItem ? targetItem->GetComponentByClass<UCableComponent>() : nullptr)
	{
		cable->SetAttachEndToComponent(cableSocket);
	}
}

void ABaseRemoteItem::CheckCableLimit()
{
	float distanceSquared = GetOwner()->GetSquaredDistanceTo(targetItem);
	if (distanceSquared > rangeSquared)
	{
		ownerInteractComponent->DropItem(false);

		FVector_NetQuantize direction = (targetItem->GetActorLocation() - GetActorLocation()).GetSafeNormal();
		itemMesh->SetPhysicsLinearVelocity(FVector::Zero());
		itemMesh->AddImpulse(direction * pullStrength);
	}
}

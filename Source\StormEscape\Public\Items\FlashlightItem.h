// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Items/BaseDeviceItem.h"
#include "Components/SpotLightComponent.h"
#include "FlashlightItem.generated.h"

/**
 * 
 */
UCLASS()
class STORMESCAPE_API AFlashlightItem : public ABaseDeviceItem
{
	GENERATED_BODY()
	
public:
	AFlashlightItem();

protected:
	virtual void BeginPlay() override;

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	USpotLightComponent* light;

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	USpotLightComponent* bleed;

protected:
	void UpdateLights(float loss = 0.f);

	void Activate_Implementation() override;

	void Update_Implementation(float deltaSeconds) override;

	void Deactivate_Implementation() override;

	virtual void OnRep_ActivatedUpdate() override;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties")
	FColor color;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties", meta = (ClampMin = "0"))
	float intensity;

	/* Intensity percent per durability. Scaled by intensity */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties")
	TSoftObjectPtr<UCurveFloat> intensityMultiplierCurve;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties", meta = (ClampMin = "0"))
	float reach;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties", meta = (ClampMin = "0", ClampMax = "90"))
	FVector2D coneAngle;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Bleed", meta = (ClampMin = "0", UIMin = "0", ClampMax = "1", UIMax = "1"))
	float bleedPercent;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Bleed", meta = (ClampMin = "0", ClampMax = "90"))
	FVector2D bleedAngle;

private:
	float GetIntensityMultiplier() const { return intensityMultiplierCurve ? intensityMultiplierCurve->GetFloatValue(durability / maxDurability) : 1.f; }

public:
	void Flicker();

protected:
	/* Minimum and maximum flickering duration. */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Flickering", meta = (ClampMin = "0"))
	FVector2D flickerDuration;

	/* Flickering oscilation rate. */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Flickering", meta = (ClampMin = "0"))
	float flickerSpeed;

	/* Maximum percentage loss when the flashlight is flickering. */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Flickering|Strength", meta = (ClampMin = "0", UIMin = "0", ClampMax = "1", UIMax = "1"))
	float flickerStrength;

	/* Strength per durability. Overwrites flickerStrength. */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Flickering|Strength")
	TSoftObjectPtr<UCurveFloat> flickerStrengthCurve;

	float GetFlickerStrength() const { return flickerStrengthCurve ? flickerStrengthCurve->GetFloatValue(durability / maxDurability) : flickerStrength; }

	/* Percentage chance of flickering occur each cooldown. */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Flickering|Chance", meta = (ClampMin = "0", UIMin = "0", ClampMax = "1", UIMax = "1"))
	float flickerChance;

	/* Chance per durability. Overwrites flickerChance. */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Flickering|Chance")
	TSoftObjectPtr<UCurveFloat> flickerChanceCurve;
	float GetFlickerChance() const { return flickerChanceCurve ? flickerChanceCurve->GetFloatValue(durability / maxDurability) : flickerChance; }

	/* Flickering check frequency. */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Flickering|CoolDown", meta = (ClampMin = "0"))
	float flickerCooldown;

	/* Cooldown per durability. Overwrites flickerCooldown. */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Flickering|CoolDown")
	TSoftObjectPtr<UCurveFloat> flickerCooldownCurve;

	float GetFlickerCooldown() const { return flickerCooldownCurve ? flickerCooldownCurve->GetFloatValue(durability / maxDurability) : flickerCooldown; }

	/* Flickering influence on the flashlight's intensity, scaled by strength. */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Flickering|Weight", meta = (ClampMin = "0", UIMin = "0", ClampMax = "1", UIMax = "1"))
	float intensityLoss;

	/* Flickering influence on the flashlight's reach, scaled by strength. */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Flickering|Weight", meta = (ClampMin = "0", UIMin = "0", ClampMax = "1", UIMax = "1"))
	float reachLoss;

	/* Flickering influence on the flashlight's cone, scaled by strength. */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Flickering|Weight", meta = (ClampMin = "0", UIMin = "0", ClampMax = "1", UIMax = "1"))
	float angleLoss;

	/* Minimum and maximum blackout duration. */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Flickering|Blackout", meta = (ClampMin = "0"))
	FVector2D blackoutDuration;

	/* Percentage chance of a blackout occur at when a flickering ends. */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Flickering|Blackout", meta = (ClampMin = "0", UIMin = "0", ClampMax = "1", UIMax = "1"))
	float blackoutChance;

	/* Blackout chance per durability. Overwrites blackoutChance. */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Flickering|Blackout")
	TSoftObjectPtr<UCurveFloat> blackoutChanceCurve;
	float GetBlackoutChance() const { return blackoutChanceCurve ? blackoutChanceCurve->GetFloatValue(durability / maxDurability) : blackoutChance; }

private:
	UFUNCTION(NetMulticast, Unreliable)
	void MulticastStartFlicker(float loss, float duration, float blackout);

	void UpdateFlicker(float loss, float duration, float blackout);

	UFUNCTION(NetMulticast, Unreliable)
	void MulticastStopFlicker();

	bool bFlickering;

	FTimerHandle flickerTimer;

	float flickerDelta = 0.05f;

	float flickeringTime;

	float workingTime;
};

// Fill out your copyright notice in the Description page of Project Settings.


#include "Items/StationaryRadio.h"
#include "Net/UnrealNetwork.h"

AStationaryRadio::AStationaryRadio()
{
	cordRestLength = 100.f;
	cordMaxLength = 200.f;
	cordPullStrength = 6000.f;

	cableComponent = CreateDefaultSubobject<UCableComponent>("Cable");
	cableComponent->SetupAttachment(GetRootComponent());
	cableComponent->EndLocation = FVector::ZeroVector;
	cableComponent->CableWidth = 2.f;

	physicsConstraint = CreateDefaultSubobject<UPhysicsConstraintComponent>("PhysicsConstraint");
	physicsConstraint->SetupAttachment(GetRootComponent());
	physicsConstraint->ConstraintInstance.EnableParentDominates();
	physicsConstraint->ConstraintInstance.bScaleLinearLimits = false;
	physicsConstraint->ConstraintInstance.SetSoftLinearLimitParams(true, 1000.f, 100.f, 1.f, 2.f);
	physicsConstraint->SetAngularDriveMode(EAngularDriveMode::TwistAndSwing);
	physicsConstraint->SetAngularVelocityDriveTwistAndSwing(true, true);

	micSocket = CreateDefaultSubobject<UArrowComponent>("MicPlacement");
	micSocket->SetupAttachment(GetRootComponent());
	cableComponent->SetAttachEndToComponent(micSocket);

	UpdateCableConstraints();
}

void AStationaryRadio::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(AStationaryRadio, microphone);
}

void AStationaryRadio::BeginPlay()
{
	Super::BeginPlay();

	if (HasAuthority())
	{
		UpdateCableConstraints();
		if (micClass)
		{
			FActorSpawnParameters SpawnInfo;
			microphone = GetWorld()->SpawnActor<ABaseRemoteItem>(micClass, micSocket->GetComponentLocation(), micSocket->GetComponentRotation(), SpawnInfo);
			microphone->SetTarget(this, cordMaxLength, cableComponent, physicsConstraint, cordPullStrength);
			microphone->OnCarrierChangedDelegate.AddUniqueDynamic(this, &AStationaryRadio::OnMicCarrierChanged);
			
			if (activationType == EActivationType::None)
			{
				microphone->ToggleRemote(false);
				UStaticMeshComponent* micMesh = IInteractionInterface::Execute_GetItemMesh(microphone);
				if (IsValid(micMesh))
				{
					cableComponent->bEnableCollision = false;
					micMesh->SetSimulatePhysics(false);
					micMesh->SetCollisionResponseToAllChannels(ECR_Ignore);
				}
			}
		}
	}
}

void AStationaryRadio::UpdateCableConstraints()
{
	cableComponent->CableLength = cordRestLength;
	physicsConstraint->SetLinearXLimit(ELinearConstraintMotion::LCM_Limited, cordRestLength);
	physicsConstraint->SetLinearYLimit(ELinearConstraintMotion::LCM_Limited, cordRestLength);
	physicsConstraint->SetLinearZLimit(ELinearConstraintMotion::LCM_Limited, cordRestLength);
}

void AStationaryRadio::OnMicCarrierChanged(AActor* micOwner)
{
	CloseMicrophone();
	SetOwner(micOwner);
}


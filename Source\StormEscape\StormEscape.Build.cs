using UnrealBuildTool;
using System.IO;

public class StormEscape : ModuleRules
{
	public StormEscape(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = PCHUsageMode.UseExplicitOrSharedPCHs;

		PublicDependencyModuleNames.AddRange(new string[] 
		{ 
			"Core", "CoreUObject", "Engine", "InputCore", "EnhancedInput", "UMG", "DeveloperSettings", 
			"AdvancedSessions", "AdvancedSteamSessions", "CableComponent", "Odin", "OdinLibrary", 
			"AudioCapture", "AudioCaptureCore", "AudioMixer", "CommonUI","Slate", "SlateCore", "Synthesis"
		});

		PrivateDependencyModuleNames.AddRange(new string[] 
		{ 
			"OnlineSubsystem", "OnlineSubsystemNull", "OnlineSubsystemSteam" 
		});

		// === Steamworks SDK Setup ===
		string SteamPath = Path.Combine(ModuleDirectory, "..", "ThirdParty");
		string SteamLibPath = Path.Combine(SteamPath, "redistributable_bin", "win64");

		PublicIncludePaths.Add(Path.Combine(SteamPath, "public")); // Steam headers
		PublicAdditionalLibraries.Add(Path.Combine(SteamLibPath, "steam_api64.lib")); // Link .lib

		// Ensure DLL is copied to output
		RuntimeDependencies.Add("$(BinaryOutputDir)/steam_api64.dll", Path.Combine(SteamLibPath, "steam_api64.dll"), StagedFileType.NonUFS);
	}
}
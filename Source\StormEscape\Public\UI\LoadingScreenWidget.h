// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "LoadingScreenWidget.generated.h"

/**
 * 
 */

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnLoadingScreenStarted);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnLoadingScreenEnded);

UENUM(BlueprintType)
enum class ELoadingRequest : uint8
{
	None = 0	UMETA(DisplayName = "None"),
	Open = 1	UMETA(DisplayName = "Open"),
	Close = 2	UMETA(DisplayName = "Close")
};

UCLASS()
class STORMESCAPE_API ULoadingScreenWidget : public UUserWidget
{
	GENERATED_BODY()
	
public:
	virtual bool Initialize() override;

	void Toggle(bool bVisible);

	bool IsDisplaying() const { return bIsDisplaying; }

	//block next loading screen
	UFUNCTION(BlueprintCallable)
	void BlockNextLoadingScreen() { bBlockNextLoadingScreen = true; }

	UPROPERTY(BlueprintAssignable)
	FOnLoadingScreenStarted OnStartedDelegate;

	UPROPERTY(BlueprintAssignable)
	FOnLoadingScreenEnded OnEndedDelegate;

protected:
	UFUNCTION(BlueprintCallable)
	void NotifyAnimationFinished(bool bOpen);

	UFUNCTION(BlueprintImplementableEvent)
	void OnEnter();

	UFUNCTION(BlueprintImplementableEvent)
	void OnExit();

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	float minDuration = 2.f;

private:
	UFUNCTION()
	void OnExpired();

	bool bIsDisplaying = false;
	bool bBlockNextLoadingScreen = false;
	bool bIsAnimating = false;
	ELoadingRequest lastRequest;
	FTimerHandle exitTimer;
};

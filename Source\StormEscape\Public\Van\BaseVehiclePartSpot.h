#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Interfaces/InteractionInterface.h"
#include "Interfaces/RepairInterface.h"
#include "BaseVehiclePartSpot.generated.h"

class AGameplayVan;

UCLASS()
class STORMESCAPE_API ABaseVehiclePartSpot : public AActor, public IInteractionInterface, public IRepairInterface
{
	GENERATED_BODY()
	
public:	

	ABaseVehiclePartSpot();

	virtual void Tick(float DeltaTime) override;

	FORCEINLINE bool CanShowProgress_Implementation(AActor* otherActor) const override { return false; }

	FORCEINLINE bool RequiresTool_Implementation() override { return true; }

	FORCEINLINE bool IsAPickeableItem_Implementation() override { return false; }

	FORCEINLINE bool IsSamePartType_Implementation(EVehiclePart InVehiclePart) override { return vehiclePart == InVehiclePart; }

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UStaticMeshComponent* spotMesh;

	AGameplayVan* vanRef;

protected:
	virtual void BeginPlay() override;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties")
	EVehiclePart vehiclePart;

private:
	void GetVanReference();
};

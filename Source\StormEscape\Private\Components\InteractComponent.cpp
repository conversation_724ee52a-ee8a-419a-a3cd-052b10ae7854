#include "Components/InteractComponent.h"
#include "Camera/CameraComponent.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Net/UnrealNetwork.h"
#include "Interfaces/InteractionInterface.h"
#include "PhysicsEngine/PhysicsConstraintComponent.h"
#include "GameFramework/Character.h"

UInteractComponent::UInteractComponent()
{
	PrimaryComponentTick.bCanEverTick = true;
	SetIsReplicatedByDefault(true);

	interactionDistance = 150.f;
	interactionRadius = 5.f;
	minThrowSrength = 6000.f;
	maxThrowSrength = 10000.f;
	maxSpeedReduction = 0.9f;
}


void UInteractComponent::InitializeInteractionComponent(UCameraComponent* InPlayerCamera, UPrimitiveComponent* InHeldItemSlot, UPhysicsConstraintComponent* InPhysicsConstraint, bool bApplyDefaultSettings)
{
	if (InPlayerCamera)
		playerCamera = InPlayerCamera;

	if (InHeldItemSlot)
		heldItemSlot = InHeldItemSlot;

	if (InPhysicsConstraint)
		grabConstraint = InPhysicsConstraint;

	if (bApplyDefaultSettings)
	{
		ApplyDefaultPhysicsSettings();
	}
}

void UInteractComponent::AddSpeedModifier(AActor* source, float percent)
{
	if (IsValid(source))
	{
		Server_AddSpeedModifier(source, percent);
	}
}

void UInteractComponent::RemoveSpeedModifier(AActor* source)
{
	if (IsValid(source) && sourceToSpeedModifierMap.Contains(source))
	{
		Server_RemoveSpeedModifier(source);
	}
}

float UInteractComponent::GetSpeedModifier() const
{
	return FMath::Clamp(speedModifier, -maxSpeedReduction, 1.f);
}

void UInteractComponent::BeginPlay()
{
	Super::BeginPlay();
}

void UInteractComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	if (playerCamera)
		TraceInteractables();
}

void UInteractComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	DOREPLIFETIME_CONDITION(UInteractComponent, hitItem, COND_OwnerOnly);
	DOREPLIFETIME_CONDITION(UInteractComponent, heldItem, COND_OwnerOnly);
	DOREPLIFETIME_CONDITION(UInteractComponent, bIsInteracting, COND_OwnerOnly);
	DOREPLIFETIME_CONDITION(UInteractComponent, speedModifier, COND_OwnerOnly);
}

void UInteractComponent::ApplyDefaultPhysicsSettings()
{
	if (heldItemSlot && grabConstraint)
	{
		heldItemSlot->SetCollisionResponseToAllChannels(ECollisionResponse::ECR_Ignore);
		heldItemSlot->bHiddenInGame = true;

		grabConstraint->SetLinearXLimit(ELinearConstraintMotion::LCM_Free, 0.f);
		grabConstraint->SetLinearYLimit(ELinearConstraintMotion::LCM_Free, 0.f);
		grabConstraint->SetLinearZLimit(ELinearConstraintMotion::LCM_Free, 0.f);
		grabConstraint->SetLinearPositionDrive(true, true, true);
		grabConstraint->SetLinearVelocityDrive(true, true, true);
		grabConstraint->SetLinearDriveParams(250.f, 30.f, 0.f);
		grabConstraint->SetAngularDriveMode(EAngularDriveMode::TwistAndSwing);
		grabConstraint->SetAngularOrientationDrive(true, true);
		grabConstraint->SetAngularVelocityDrive(true, true);
		grabConstraint->SetAngularDriveParams(1000.f, 100.f, 0.f);
	}
}

bool UInteractComponent::GetTraceResult(float reach, FHitResult& outResult)
{
	FVector startLocation = playerCamera->GetComponentLocation();
	FVector forwardVector = playerCamera->GetForwardVector();
	FVector endLocation = startLocation + (forwardVector * reach);

	TArray<AActor*> actorsToIgnore;
	actorsToIgnore.Add(GetOwner());
	if (heldItem)
	{
		actorsToIgnore.Add(heldItem);
	}

	ETraceTypeQuery traceChannel = UEngineTypes::ConvertToTraceType(ECC_Visibility);

	return UKismetSystemLibrary::SphereTraceSingle(
		GetWorld(),
		startLocation,
		endLocation,
		interactionRadius,
		traceChannel,
		false,
		actorsToIgnore,
		EDrawDebugTrace::None,
		outResult,
		true
	);
}


void UInteractComponent::TraceInteractables()
{
	FHitResult hitResult;
	if (GetTraceResult(interactionDistance, hitResult) && IsValid(hitResult.GetActor()))
	{
		CheckForInteractables(hitResult.GetActor());
	}
	else
	{
		ClearInteractables();
	}
}

void UInteractComponent::CheckForInteractables(AActor* hitActor)
{
	if (hitActor)
	{
		if (hitActor->ActorHasTag("Body"))
		{
			Server_CheckForInteractables(hitActor);
		}
		else if (hitActor->GetClass()->ImplementsInterface(UInteractionInterface::StaticClass()) && !IInteractionInterface::Execute_IsHeld(hitActor))
		{
			Server_CheckForInteractables(hitActor);
		}
		else
		{
			ClearInteractables();
		}
	}
	else
	{
		ClearInteractables();
	}
}

void UInteractComponent::Server_CheckForInteractables_Implementation(AActor* item)
{
	if (!hitItem)
	{
		SetHitItem(item);
	}
	else if (hitItem != item)
	{
		StopInteraction();
		SetHitItem(item);
	}
}

void UInteractComponent::SetHitItem(AActor* item)
{
	AActor* oldItem = hitItem;
	hitItem = item;
	OnRep_HitItemUpdate(oldItem);

	if (IsValid(hitItem) && hitItem->GetClass()->ImplementsInterface(UInteractionInterface::StaticClass()))
	{
		IInteractionInterface::Execute_NotifyFocus(hitItem, GetOwner(), this);
	}
	
	if (IsValid(oldItem) && oldItem->GetClass()->ImplementsInterface(UInteractionInterface::StaticClass()))
	{
		IInteractionInterface::Execute_NotifyUnfocus(oldItem, GetOwner(), this);
	}
}

void UInteractComponent::OnRep_HitItemUpdate(AActor* oldItem)
{
	if (IsValid(hitItem) || !IsValid(heldItem) || oldItem != heldItem)
	{
		OnFocusItemChangedDelegate.Broadcast(hitItem);
	}
}

void UInteractComponent::ClearInteractables()
{
	if (hitItem)
	{
		Server_ClearInteractables();
	}
}

void UInteractComponent::Server_ClearInteractables_Implementation()
{
	if (hitItem)
	{
		StopInteraction();
		SetHitItem(nullptr);
	}
}

bool UInteractComponent::IsHoldingItem()
{
	if (heldItem)
	{
		return true;
	}
	return false;
}

void UInteractComponent::ExecuteInteractEvent(AActor* item)
{
	if (item)
	{
		IInteractionInterface::Execute_Interact(item, GetOwner(), this);
	}
}

void UInteractComponent::StartInteraction(bool bWithTool)
{
	if (hitItem)
	{
		if (hitItem->ActorHasTag("Body"))
		{
			PerformGrab(hitItem);
		}
		else if (IInteractionInterface::Execute_IsAPickeableItem(hitItem))
		{
			PerformGrab(hitItem);
		}
		else if (!IInteractionInterface::Execute_IsAPickeableItem(hitItem) && IInteractionInterface::Execute_CanBeInteracted(hitItem))
		{
			Server_StartInteraction(bWithTool);
		}
	}
}

void UInteractComponent::Server_StartInteraction_Implementation(bool bWithTool)
{
	if (hitItem && !IInteractionInterface::Execute_IsAPickeableItem(hitItem))
	{
		if (IInteractionInterface::Execute_RequiresTool(hitItem) != bWithTool)
			return;

		ExecuteInteractEvent(hitItem);
	}
}

void UInteractComponent::SetIsInteracting()
{
	if(hitItem)
		Server_SetIsInteracting();
}

void UInteractComponent::Server_SetIsInteracting_Implementation()
{
	if (hitItem)
	{
		bIsInteracting = true;
		IInteractionInterface::Execute_NotifyStartAction(hitItem, GetOwner(), this);
	}
}

void UInteractComponent::Client_UpdateInteractionBar_Implementation(AActor* item, float InInteractionTime, float InActionDuration)
{
	OnInteractionProgressChangedDelegate.Broadcast(item, InInteractionTime / InActionDuration);
}

void UInteractComponent::NotifyProgress(AActor* item, float progressTime, float duration)
{
	Client_UpdateInteractionBar(item, progressTime, duration);
}

void UInteractComponent::Client_ApplyInteractionSettings_Implementation(AActor* item)
{
	OnItemSettingsChangedDelegate.Broadcast(item);
}

void UInteractComponent::NotifyItemSettingsUpdate(AActor* item)
{
	if (IsValid(item))
	{
		Client_ApplyInteractionSettings(item);
	}
}

void UInteractComponent::StopInteraction()
{
	if (bIsInteracting)
	{
		Server_StopInteraction();
	}
}

void UInteractComponent::Server_StopInteraction_Implementation()
{
	if (bIsInteracting)
	{
		bIsInteracting = false;
		if (hitItem)
		{
			IInteractionInterface::Execute_NotifyStopAction(hitItem, GetOwner(), this);
			ExecuteInterruptEvent(hitItem);
		}
	}
}

void UInteractComponent::ExecuteInterruptEvent(AActor* item)
{
	if (item && !item->ActorHasTag("Body"))
	{
		IInteractionInterface::Execute_Interrupt(item, GetOwner(), this);
	}
}

void UInteractComponent::PerformGrab(AActor* item)
{
	if (grabConstraint && heldItemSlot && !heldItem && item)
	{
		if (item->ActorHasTag("Body"))
			Server_Grab_Body(item);
		else
			Server_Grab(item);
	}
}

void UInteractComponent::Server_Grab_Implementation(AActor* item)
{
	if (grabConstraint && heldItemSlot && !heldItem && item)
	{
		heldItem = item;
		IInteractionInterface::Execute_NotifyPickUp(heldItem, GetOwner(), this);

		UStaticMeshComponent* itemMesh = IInteractionInterface::Execute_GetItemMesh(heldItem);
		if (itemMesh)
		{
			FVector_NetQuantize impulse = GetOwner()->GetActorUpVector() * -15000.f;
			Multicast_Grab(heldItem, itemMesh, impulse);
		}

		ClearInteractables();
	}
}

void UInteractComponent::Multicast_Grab_Implementation(AActor* item, UStaticMeshComponent* itemMesh, FVector_NetQuantize impulse)
{
	OnHeldItemChangedDelegate.Broadcast(item);
	if (itemMesh->IsSimulatingPhysics())
	{
		itemMesh->SetCollisionResponseToAllChannels(ECR_Ignore);
		item->SetActorLocation(heldItemSlot->GetComponentLocation(), false, nullptr, ETeleportType::TeleportPhysics);
		item->SetActorRotation(heldItemSlot->GetComponentRotation(), ETeleportType::TeleportPhysics);
		grabConstraint->SetConstrainedComponents(heldItemSlot, FName("None"), itemMesh, FName("None"));
		itemMesh->AddImpulse(impulse);
	}
}

void UInteractComponent::Server_Grab_Body_Implementation(AActor* otherPlayer)
{
	if (grabConstraint && heldItemSlot && !heldItem && otherPlayer)
	{
		if (ACharacter* playerRef = Cast<ACharacter>(otherPlayer))
		{
			heldItem = otherPlayer;
			FVector_NetQuantize impulse = GetOwner()->GetActorUpVector() * -15000.f;
			grabConstraint->SetLinearDriveParams(2000.f, 200.f, 0.f);
			Multicast_Grab_Body(playerRef->GetMesh(), impulse);
			ClearInteractables();
		}
	}
}

void UInteractComponent::Multicast_Grab_Body_Implementation(USkeletalMeshComponent* bodyMesh, FVector_NetQuantize impulse)
{
	if (bodyMesh->IsSimulatingPhysics())
	{
		FVector_NetQuantize grabLocation = heldItemSlot->GetComponentLocation();
		heldItemSlot->SetWorldLocation(bodyMesh->GetSocketLocation("spine_03"), false, nullptr, ETeleportType::TeleportPhysics);
		bodyMesh->SetCollisionResponseToAllChannels(ECR_Ignore);
		grabConstraint->SetConstrainedComponents(heldItemSlot, FName("None"), bodyMesh, FName("spine_03"));
		heldItemSlot->SetWorldLocation(grabLocation);
		bodyMesh->AddImpulse(impulse);
	}
}

void UInteractComponent::DropItem(const bool bApplyImpulse)
{
	if (grabConstraint && heldItemSlot && heldItem)
	{
		if (heldItem->ActorHasTag("Body"))
			Server_DropBody();
		else
			Server_DropItem(bApplyImpulse);
	}
}

void UInteractComponent::Server_DropItem_Implementation(const bool bApplyImpulse)
{
	if (grabConstraint && heldItemSlot && heldItem)
	{
		IInteractionInterface::Execute_NotifyDrop(heldItem, GetOwner(), this);
		UStaticMeshComponent* itemMesh = IInteractionInterface::Execute_GetItemMesh(heldItem);

		if (bApplyImpulse)
		{
			float vel = abs(itemMesh->GetPhysicsLinearVelocity().Length()) * 40.f;
			vel = FMath::Clamp(minThrowSrength + vel, minThrowSrength, maxThrowSrength);
			FVector_NetQuantize impulse = playerCamera->GetForwardVector() * vel;
			Multicast_DropItem(itemMesh, impulse);
		}
		else
		{
			Multicast_DropItemSimple(itemMesh);
		}

		heldItem = nullptr;
	}
}

void UInteractComponent::Multicast_DropItem_Implementation(UStaticMeshComponent* itemMesh, FVector_NetQuantize impulse)
{
	OnHeldItemChangedDelegate.Broadcast(nullptr);
	grabConstraint->BreakConstraint();
	itemMesh->SetPhysicsLinearVelocity(FVector::Zero());
	itemMesh->AddImpulse(impulse);
	itemMesh->SetCollisionResponseToAllChannels(ECR_Block);
	itemMesh->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
}

void UInteractComponent::Multicast_DropItemSimple_Implementation(UStaticMeshComponent* itemMesh)
{
	OnHeldItemChangedDelegate.Broadcast(nullptr);
	grabConstraint->BreakConstraint();
	itemMesh->SetCollisionResponseToAllChannels(ECR_Block);
	itemMesh->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
}

void UInteractComponent::Server_DropBody_Implementation()
{
	if (grabConstraint && heldItemSlot && heldItem)
	{
		if (ACharacter* playerRef = Cast<ACharacter>(heldItem))
		{
			USkeletalMeshComponent* bodyMesh = playerRef->GetMesh();

			float vel = abs(bodyMesh->GetPhysicsLinearVelocity().Length()) * 40.f;
			vel = FMath::Clamp(minThrowSrength + vel, minThrowSrength, maxThrowSrength);
			FVector_NetQuantize impulse = playerCamera->GetForwardVector() * vel;
			grabConstraint->SetLinearDriveParams(250.f, 30.f, 0.f);

			Multicast_DropBody(bodyMesh, impulse);;
			heldItem = nullptr;
		}
	}
}

void UInteractComponent::Multicast_DropBody_Implementation(USkeletalMeshComponent* bodyMesh, FVector_NetQuantize impulse)
{
	if (bodyMesh)
	{
		grabConstraint->BreakConstraint();
		bodyMesh->SetPhysicsLinearVelocity(FVector::Zero());
		bodyMesh->AddImpulse(impulse);
		bodyMesh->SetCollisionResponseToAllChannels(ECR_Block);
		bodyMesh->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
	}
}

void UInteractComponent::Server_AddSpeedModifier_Implementation(AActor* source, float percent)
{
	if (IsValid(source))
	{
		sourceToSpeedModifierMap.FindOrAdd(source) = percent;
		UpdateSpeedModifier();
	}
}

void UInteractComponent::Server_RemoveSpeedModifier_Implementation(AActor* source)
{
	if (IsValid(source) && sourceToSpeedModifierMap.Contains(source))
	{
		sourceToSpeedModifierMap.Remove(source);
		UpdateSpeedModifier();
	}
}

void UInteractComponent::UpdateSpeedModifier()
{
	float newModifier = 0.f;
	for (TPair<AActor*, float> entry : sourceToSpeedModifierMap)
	{
		newModifier += entry.Value;
	}

	if (!FMath::IsNearlyEqual(speedModifier, newModifier))
	{
		speedModifier = newModifier;
		OnRep_SpeedModifierUpdate();
	}
}

void UInteractComponent::OnRep_SpeedModifierUpdate()
{
	OnSpeedModifierChangedDelegate.Broadcast(speedModifier);
}
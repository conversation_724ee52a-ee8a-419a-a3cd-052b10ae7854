// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "Weather/Tornado.h"
#include "WeatherSubsystem.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogWeather, Log, All);

/**
 * 
 */
UCLASS()
class STORMESCAPE_API UWeatherSubsystem : public UWorldSubsystem
{
	GENERATED_BODY()
	
public:
	void SpawnTornado(TSubclassOf<ATornado> tornadoClass);

private:
	ATornado* tornado;
};

// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ArrowComponent.h"
#include "Items/ItemSpawnTypes.h"
#include "ItemHookComponent.generated.h"

/**
 * 
 */
UCLASS( ClassGroup = (Custom), meta = (BlueprintSpawnableComponent) )
class STORMESCAPE_API UItemHookComponent : public UArrowComponent
{
	GENERATED_BODY()
	
public:
	UItemHookComponent();

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	EItemSize GetItemSize() const { return itemSize; }

	void SpawnItem(const FItemSpawnData& data);

protected:
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	EItemSize itemSize;

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	bool bConstrainItem;

	UFUNCTION()
	void OnRep_ItemUpdate(ABaseItem* oldItem);

	UPROPERTY(ReplicatedUsing = OnRep_ItemUpdate)
	ABaseItem* owningItem = nullptr;

private:
	UFUNCTION()
	void OnItemPickedUp(AActor* carrier);

	void ToggleHook(ABaseItem* item, bool bHooked);
};

// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Items/BaseDeviceItem.h"
#include "Interfaces/DamageInterface.h"
#include "DefibrillatorItem.generated.h"

/**
 * 
 */
UCLASS()
class STORMESCAPE_API ADefibrillatorItem : public ABaseDeviceItem
{
	GENERATED_BODY()
	
public:
	ADefibrillatorItem();

	void NotifyPickUp_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	void NotifyDrop_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

protected:

	void Activate_Implementation() override;

	void Deactivate_Implementation() override;

	void Complete_Implementation() override;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Defibrillator")
	float shockRange = 200.f; 
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Defibrillator", meta = (ClampMin = "0", UIMin = "0", ClampMax = "1", UIMax = "1"))
	float speedReduction = 0.5f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Durability")
	float resurrectionCost = 1.f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Defibrillator")
	float healingAmount = 10.f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Defibrillator")
	float stunDuration = 5.f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Defibrillator")
	float pushStrength = 10000.f;

	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnInvalidTarget")
	void K2_OnInvalidTarget(bool bHit, const FHitResult& hitResult);

	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnTargetRevived")
	void K2_OnTargetRevived(AActor* target, bool bSuccess);

	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnTargetStunned")
	void K2_OnTargetStunned(AActor* target);

private:
	UFUNCTION(NetMulticast, Unreliable)
	void Multicast_OnShockReleased(const FHitResult& hitResult, EActorHealthStatus targetStatus = EActorHealthStatus::None);
};

// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/HUD.h"
#include "StormEscapeHUD.generated.h"

/**
 * 
 */
UCLASS()
class STORMESCAPE_API AStormEscapeHUD : public AHUD
{
	GENERATED_BODY()
	
public:
	void NotifyPlayerDeath();

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void ToggleUI(bool bShowUI);

protected:
	UFUNCTION(BlueprintImplementableEvent, DisplayName="OnPlayerDeath")
	void K2_OnPlayerDeath();
};

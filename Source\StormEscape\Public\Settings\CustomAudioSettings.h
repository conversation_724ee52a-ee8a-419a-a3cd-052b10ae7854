// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DeveloperSettings.h"
#include "SourceEffects/SourceEffectSimpleDelay.h"
#include "CustomAudioSettings.generated.h"

/**
 * 
 */
UCLASS(Config = Game, defaultconfig, meta = (DisplayName = "Custom Audio Settings"))
class STORMESCAPE_API UCustomAudioSettings : public UDeveloperSettings
{
	GENERATED_BODY()
	
public:
	float GetRadioDelay() const;

protected:
	/* The widget class to be used as a loading screen */
	UPROPERTY(Config, EditAnywhere, BlueprintReadOnly, Category = "Radio")
	TSoftObjectPtr<USourceEffectSimpleDelayPreset> radioDelayEffect;

	
};

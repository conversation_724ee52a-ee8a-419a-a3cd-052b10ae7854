#pragma once

#include "CoreMinimal.h"
#include "Van/BaseCap.h"
#include "FuelCap.generated.h"

class AGameplayVan;
class UCableComponent;
class UPhysicsConstraintComponent;

UCLASS()
class STORMESCAPE_API AFuelCap : public ABaseCap
{
	GENERATED_BODY()
	
public:
	AFuelCap();

protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UCableComponent* cableComponent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UPhysicsConstraintComponent* physicsConstraint;

	virtual void Multicast_Open_Implementation() override;

	virtual void Multicast_Close_Implementation() override;
};

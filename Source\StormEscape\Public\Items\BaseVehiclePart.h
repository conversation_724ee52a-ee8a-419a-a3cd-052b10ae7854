#pragma once

#include "CoreMinimal.h"
#include "Items/BaseItem.h"
#include "Interfaces/RepairInterface.h"
#include "BaseVehiclePart.generated.h"

class UCameraComponent;
class UInteractComponent;
class AGameplayVan;
class ABaseVehiclePartSpot;

UCLASS()
class STORMESCAPE_API ABaseVehiclePart : public ABaseItem, public IRepairInterface
{
	GENERATED_BODY()
	
public:
	ABaseVehiclePart();

#if WITH_EDITOR
	virtual void OnConstruction(const FTransform& Transform) override;
#endif

	AGameplayVan* vanRef;

	void Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	void Interrupt_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	FORCEINLINE bool RequiresTool_Implementation() override { return bRequiresToolBox; }

	FORCEINLINE bool IsSamePartType_Implementation(EVehiclePart InVehiclePart) override { return vehiclePart == InVehiclePart; }

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Properties|Repairing")
	EVehiclePart vehiclePart;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Properties|Repairing")
	bool bIsRemovable;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Repairing")
	bool bIsDamaged;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Properties|Repairing")
	bool bRequiresToolBox;

protected:
	virtual void BeginPlay() override;

	virtual void Complete_Implementation() override;

	UPROPERTY(BlueprintReadOnly)
	ABaseVehiclePartSpot* vehiclePartSpot;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties|Repairing")
	float timeToRemove;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties|Repairing")
	float timeToInsert;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties|Repairing")
	float timeToInstall;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties|Repairing")
	UMaterialInterface* baseMaterial;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Properties|Repairing")
	UMaterialInterface* damagedMaterial;

	virtual void SwapVehicleParts(UPrimitiveComponent* InVehiclePartSpot);

	UFUNCTION(BlueprintNativeEvent, BlueprintCallable)
	void ApplyTransparency(bool bShouldApply);

	virtual void RemovePart();

	virtual void InsertPart();

	virtual void InstallPart();

private:
	void GetSpot();

	void GetVanReference();

	void TraceVehiclePartSpot(UInteractComponent* interactComponent);

	UFUNCTION(NetMulticast, Reliable)
	void Multicast_SwapVehicleParts(UPrimitiveComponent* InVehiclePartSpot);
};

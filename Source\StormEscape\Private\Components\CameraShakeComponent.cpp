#include "Components/CameraShakeComponent.h"
#include "Kismet/GameplayStatics.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Shakes/LegacyCameraShake.h"

UCameraShakeComponent::UCameraShakeComponent()
{
	PrimaryComponentTick.bCanEverTick = true;

	shakeIntensity = 0.1f;
}

void UCameraShakeComponent::BeginPlay()
{
	Super::BeginPlay();
	
	characterRef = Cast<ACharacter>(GetOwner());
	if (characterRef)
	{
		playerController = Cast<APlayerController>(characterRef->Controller);
		movementComponent = characterRef->GetCharacterMovement();
	}
}

void UCameraShakeComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);

	if (characterRef && bIsActive)
	{
		PerformIdle();
	}
}

void UCameraShakeComponent::StartShake(EShakeType shakeType, float scale)
{
	if (!bIsActive)
		return;

	if (shakeType == EShakeType::Idle)
		ExecuteShake(idleShake, scale);
	else if (shakeType == EShakeType::Walking)
		ExecuteShake(walkingShake, scale);
	else if (shakeType == EShakeType::Running)
		ExecuteShake(runningShake, scale);
	else if (shakeType == EShakeType::Jumping)
		ExecuteShake(jumpShake, scale);
	else if (shakeType == EShakeType::Landing)
		ExecuteShake(landShake, scale);
	else if (shakeType == EShakeType::HardLanding)
		ExecuteShake(hardLandShake, scale);
	else if (shakeType == EShakeType::HardDamage)
		ExecuteShake(HardDamageShake, scale);
	else if (shakeType == EShakeType::SoftDamage)
		ExecuteShake(SoftDamageShake, scale);
}

void UCameraShakeComponent::ExecuteShake(TSubclassOf<ULegacyCameraShake> shakeClass, const float scale)
{
	if (shakeClass && playerController)
	{
		currentIntensity = scale;
		if (shakeClass != idleShake && shakeClass != walkingShake && shakeClass != runningShake)
		{
			shakeIntensity = scale;
			lastShake = shakeClass;
		}
		else if (shakeClass != lastShake)
		{
			lastShake = shakeClass;
			shakeIntensity = scale / 5.f;
			GetWorld()->GetTimerManager().SetTimer(shakeHandle, this, &UCameraShakeComponent::IncreaseShakeIntensity, 0.02f, true);
		}
		else if (shakeIntensity > currentIntensity)
		{
			shakeIntensity = currentIntensity;
		}
		playerController->ClientStartCameraShake(shakeClass, shakeIntensity);
	}
}

void UCameraShakeComponent::PerformIdle()
{
	if (characterRef->GetVelocity().Length() <= 10.f && movementComponent->CanEverJump())
	{
		ExecuteShake(idleShake, 1.f);
	}
}

void UCameraShakeComponent::IncreaseShakeIntensity()
{
	shakeIntensity += 0.02;
	if (shakeIntensity >= currentIntensity)
	{
		GetWorld()->GetTimerManager().ClearTimer(shakeHandle);
		shakeIntensity = currentIntensity;
	}
}

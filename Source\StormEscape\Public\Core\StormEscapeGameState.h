#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameStateBase.h"
#include "StormEscapeGameState.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnAllPlayersSpawned);

UCLASS()
class STORMESCAPE_API AStormEscapeGameState : public AGameStateBase
{
	GENERATED_BODY()

public:
	UPROPERTY(BlueprintAssignable, BlueprintCallable)
	FOnAllPlayersSpawned OnAllPlayersSpawnedDelegate;
	
	UFUNCTION(BlueprintCallable)
	void EnablePlayersInput();

private:
	UFUNCTION(NetMulticast, Reliable)
	void Multicast_EnableInput();
};

﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "OdinRoom.h"
#include "LobbyVoiceChatComponent.generated.h"


class UOdinSynthComponent;
class UOdinAudioCapture;
class UOdinTokenGenerator;

UCLASS(Blueprintable ,ClassGroup=(Custom), meta=(BlueprintSpawnableComponent))
class STORMESCAPE_API ULobbyVoiceChatComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	
	ULobbyVoiceChatComponent();

	UFUNCTION(BlueprintCallable)
	void ConnectToVoiceChat();

protected:
	virtual void BeginPlay() override;
	void TryStartVoiceChat();

	/* Local Variables */
	TMap<int64, TObjectPtr<UOdinSynthComponent>> IdSynthMap;

	UPROPERTY()
	UOdinTokenGenerator* tokenGenerator;

	UPROPERTY()
	FString roomToken;

	UPROPERTY()
	UOdinRoom* room;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly)
	FOdinApmSettings AudioSettings;
	
	UPROPERTY()
	UOdinAudioCapture* capture;

	/* Delegates and CallBacks */
	FOdinRoomJoinError OnRoomJoinError;
	FOdinRoomJoinSuccess OnRoomJoinSuccess;
	FOdinRoomAddMediaError OnAddMediaError;
	FOdinRoomAddMediaSuccess OnAddMediaSuccess;

	UFUNCTION()
	virtual void OnOthersMediaAdded(int64 peerId, UOdinPlaybackMedia* media, UOdinJsonObject* properties, UOdinRoom* odinRoom);
	UFUNCTION()
	virtual void OnOthersMediaRemoved(int64 peerId,UOdinPlaybackMedia* media, UOdinRoom* odinRoom);
	UFUNCTION()
	virtual void OnOdinPeerJoined(int64 peerId, FString userId,const TArray<uint8>& userData, UOdinRoom* odinRoom);
	UFUNCTION()
	virtual void OnOdinPeerLeft(int64 peerId, UOdinRoom* odinRoom);
	UFUNCTION()
	virtual void OnJoinedOdinRoom(FString RoomId, const TArray<uint8>& RoomUserData, FString Customer, int64 OwnPeerId, FString OwnUserId);
	UFUNCTION()
	virtual void OnOdinErrorHandler(int64 ErrorCode);

};

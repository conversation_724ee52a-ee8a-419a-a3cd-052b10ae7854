#include "Van/BaseCap.h"
#include "Van/GameplayVan.h"
#include "Components/SphereComponent.h"
#include "Net/UnrealNetwork.h"

ABaseCap::ABaseCap()
{
	capMesh = CreateDefaultSubobject<UStaticMeshComponent>("CapMesh");
	capMesh->SetupAttachment(itemMesh);

	itemCollision->SetCollisionResponseToAllChannels(ECR_Ignore);
	itemMesh->SetCollisionResponseToAllChannels(ECR_Ignore);
	itemMesh->SetCollisionResponseToChannel(ECC_Visibility, ECR_Block);
	itemMesh->SetCollisionEnabled(ECollisionEnabled::QueryOnly);

	progressType = EProgressType::Activation;
	actionDuration = 0.f;
	timeToInsert = 2.f;
	timeToRemove = 2.f;
	bRequiresToolBox = false;
	bIsPickable = false;
	bIsOpen = false;
	bActivatesOnce = false;
}

void ABaseCap::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(ABaseCap, bIsOpen);
}

void ABaseCap::BeginPlay()
{
	Super::BeginPlay();

	actionDuration = timeToRemove;

	FTimerHandle timer;
	GetWorld()->GetTimerManager().SetTimer(timer, this, &ABaseCap::GetVanReference, 0.1f, false);
}

void ABaseCap::Complete_Implementation()
{
	if (!bIsOpen)
		OpenCap();
	else
		CloseCap();

	Super::Complete_Implementation();
}

void ABaseCap::GetVanReference()
{
	if (GetOwner())
	{
		vanRef = Cast<AGameplayVan>(GetOwner());
	}
}

void ABaseCap::OpenCap()
{
	bIsOpen = true;
	actionDuration = timeToInsert;
	Multicast_Open();
}

void ABaseCap::Multicast_Open_Implementation()
{
	capMesh->SetRelativeLocationAndRotation(capOffset.GetLocation(), capOffset.GetRotation());
}

void ABaseCap::CloseCap()
{
	bIsOpen = false;
	actionDuration = timeToRemove;
	Multicast_Close();
}

void ABaseCap::Multicast_Close_Implementation()
{
	capMesh->SetRelativeLocationAndRotation(FVector(0.f, 0.f, 22.0f), FRotator(0.f, 0.f, 0.f));
}


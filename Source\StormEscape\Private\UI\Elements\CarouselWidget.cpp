// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/Elements/CarouselWidget.h"
#include "Components/Button.h"

void UCarouselWidget::Setup(const TArray<FName>& InOptions, int DefaultOption)
{
	Options = InOptions;
	MaxIndex = InOptions.Num() - 1;

	SelectedIndex = FMath::Min(DefaultOption, MaxIndex);
	if (!Options.IsEmpty())
	{
		K2_OnSetup(Options[DefaultOption]);
	}
}

void UCarouselWidget::Previous()
{
	if (!Options.IsEmpty())
	{
		SelectedIndex--;
		if (SelectedIndex < 0)
		{
			SelectedIndex = bLoopAround ? MaxIndex : 0;
		}

		K2_OnOptionChanged(Options[SelectedIndex]);
	}
}

void UCarouselWidget::Next()
{
	if (!Options.IsEmpty())
	{
		SelectedIndex++;
		if (SelectedIndex >= Options.Num())
		{
			SelectedIndex = bLoopAround ? 0 : MaxIndex;
		}

		K2_OnOptionChanged(Options[SelectedIndex]);
	}
}

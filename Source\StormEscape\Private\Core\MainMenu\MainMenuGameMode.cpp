// Fill out your copyright notice in the Description page of Project Settings.


#include "Core/MainMenu/MainMenuGameMode.h"
#include "Core/StormEscapeGameInstance.h"

AActor* AMainMenuGameMode::ChoosePlayerStart_Implementation(AController* Player)
{
	if (UStormEscapeGameInstance* GI = UStormEscapeGameInstance::GetActive_GameInstance(this))
	{
		if (GI->bFirstLaunch)
		{
			GI->bFirstLaunch = false;

			// Use FindPlayerStart with tag
			AActor* StartSpot = FindPlayerStart(Player, TEXT("StartGame"));
			if (StartSpot)
			{
				return StartSpot;
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("No PlayerStart found with tag 'StartGame'. Falling back to default."));
			}
		}
	}

	// Default behavior (includes fallback)
	return Super::ChoosePlayerStart_Implementation(Player);
}

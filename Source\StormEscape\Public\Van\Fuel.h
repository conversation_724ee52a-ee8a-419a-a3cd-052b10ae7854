#pragma once

#include "CoreMinimal.h"
#include "Items/BaseItem.h"
#include "Fuel.generated.h"

class AGameplayVan;

UCLASS()
class STORMESCAPE_API AFuel : public ABaseItem
{
	GENERATED_BODY()

public:
	AFuel();

	void Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	void Interrupt_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	UFUNCTION(BlueprintCallable, BlueprintPure)
	float GetCurrentFuel();

protected:
	virtual void BeginPlay() override;

	virtual void Process_Implementation(float deltaSeconds) override;

	virtual void Deactivate_Implementation() override;

	void NotifyDrop_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties", meta = (ClampMin = "1.0", ClampMax = "12.0"))
	float amountOfFuel;

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void UpdateWidget(float value);
private:
	AGameplayVan* vanRef;

	float fuelUsed;

	void TraceCap(const bool bOnlyTrace);

	void UpdateWeight();
};

#pragma once

#include "BaseItem.h"
#include "ItemSpawnTypes.generated.h"

UENUM(BlueprintType)
enum class EItemCategory : uint8
{
	Communication = 0	UMETA(DisplayName = "Communication"),
	Lights = 1			UMETA(DisplayName = "Lights"),
	Medical = 2			UMETA(DisplayName = "Medical"),
	Tool = 3			UMETA(DisplayName = "Tool")
};

UENUM(BlueprintType)
enum class EItemSize : uint8
{
	Small = 0	UMETA(DisplayName = "Small"),
	Medium = 1	UMETA(DisplayName = "Medium"),
	Large = 2	UMETA(DisplayName = "Large")
};

USTRUCT(BlueprintType)
struct FItemSpawnData
{
	GENERATED_BODY()

public:
	FItemSpawnData() : itemClass(nullptr), itemCategory(EItemCategory::Tool), itemSize(EItemSize::Small), customRotation(FRotator::ZeroRotator), spawnWeight(1.f), bAvailable(true) {}

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	TSubclassOf<ABaseItem> itemClass;

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	EItemCategory itemCategory;

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	EItemSize itemSize;

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	FRotator customRotation;

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	float spawnWeight;

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	bool bAvailable;

};

USTRUCT(BlueprintType)
struct FItemBenchData
{
	GENERATED_BODY()

public:
	FItemBenchData() : itemSize(EItemSize::Small), spawnLimit(1), spawnChance(1.f) {}
	FItemBenchData(EItemSize inSize, int inLimit, float inChance) : itemSize(inSize), spawnLimit(inLimit), spawnChance(inChance) {}

	UPROPERTY(EditAnywhere)
	EItemSize itemSize;

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	int spawnLimit;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, meta = (Clampmin = "0", UIMIn = "0", ClampMax = "1", UIMax = "1"))
	float spawnChance;
};

#pragma once

#include "CoreMinimal.h"
#include "Items/BaseItem.h"
#include "AntiFreeze.generated.h"

class AGameplayVan;
class ACoolantCap;

UCLASS()
class STORMESCAPE_API AAntiFreeze : public ABaseItem
{
	GENERATED_BODY()
	
public:
	AAntiFreeze();

	void Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	void Interrupt_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	UFUNCTION(BlueprintCallable, BlueprintPure)
	float GetCurrentAntiFreeze();

protected:
	virtual void BeginPlay() override;

	virtual void Process_Implementation(float deltaSeconds) override;

	virtual void Deactivate_Implementation() override;

	void NotifyDrop_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties", meta = (ClampMin = "1.0", ClampMax = "6.0"))
	float amountOfAntiFreeze;

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable)
	void UpdateWidget(float value);
private:
	AGameplayVan* vanRef;

	ACoolantCap* coolantCap;

	void TraceCap(const bool bOnlyTrace);

	void UpdateWeight();
};

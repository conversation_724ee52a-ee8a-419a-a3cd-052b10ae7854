// Fill out your copyright notice in the Description page of Project Settings.


#include "Items/WalkieTalkieItem.h"
#include "Net/UnrealNetwork.h"

AWalkieTalkieItem::AWalkieTalkieItem()
{
	bIsPickable = true;
}

void AWalkieTalkieItem::NotifyDrop_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	Super::NotifyDrop_Implementation(interactor, interactComponent);
	CloseMicrophone();
}

float AWalkieTalkieItem::GetConsume() const
{
	float multiplier = bIsMicOpen ? 2.f : 1.f;
	return consumePerSecond * multiplier;
}

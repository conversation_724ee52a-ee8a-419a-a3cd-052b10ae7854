﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "Core/Lobby/LobbyGameState.h"
#include "Net/UnrealNetwork.h"

void ALobbyGameState::SetPlayMapInfo(const FPlayMapInfo& MapInfo)
{
	CurrentPlayMap = MapInfo;
	OnRep_CurrentPlayMap();
}

void ALobbyGameState::AddPlayerInfo(const FCrewMember& Info)
{
	PlayerInfoList.Add(Info);
	OnRep_PlayerInfoList(); 
}

void ALobbyGameState::RemovePlayerInfoByName(const FString& PlayerName)
{
	const int32 Index = PlayerInfoList.IndexOfByPredicate([&](const FCrewMember& Info)
	{
		return Info.PlayerName == PlayerName;
	});

	if (Index != INDEX_NONE)
	{
		PlayerInfoList.RemoveAt(Index);
		OnRep_PlayerInfoList(); // Trigger update on clients
	}
}


void ALobbyGameState::OnRep_CurrentPlayMap()
{
	OnMapChanged.Broadcast(CurrentPlayMap);
}

void ALobbyGameState::OnRep_PlayerInfoList()
{
	OnPlayerListChanged.Broadcast(PlayerInfoList);
}

void ALobbyGameState::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);
	DOREPLIFETIME(ALobbyGameState, PlayerInfoList);
	DOREPLIFETIME(ALobbyGameState, CurrentPlayMap);
}
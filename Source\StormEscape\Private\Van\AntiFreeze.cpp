#include "Van/AntiFreeze.h"
#include "Camera/CameraComponent.h"
#include "Components/InteractComponent.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Van/GameplayVan.h"
#include "Van/CoolantCap.h"

AAntiFreeze::AAntiFreeze()
{
	bIsPickable = true;
	itemMesh->SetSimulatePhysics(true);
	amountOfAntiFreeze = 6.f;
	progressType = EProgressType::Persistent;
	bActivatesOnce = true;
}

void AAntiFreeze::BeginPlay()
{
	Super::BeginPlay();

	UpdateWeight();
	UpdateWidget(GetCurrentAntiFreeze());
}

void AAntiFreeze::Process_Implementation(float deltaSeconds)
{
	if (progressType > EProgressType::None && ShouldProgress() && vanRef && coolantCap)
	{
		progress += deltaSeconds;
		OnRep_ProgressUpdate();

		vanRef->FillFreezeTank(deltaSeconds);
		UpdateWeight();
		UpdateWidget(GetCurrentAntiFreeze());

		TraceCap(true);

		if (progress >= actionDuration)
		{
			Complete();
			vanRef = nullptr;
			coolantCap = nullptr;
			SetActionDuration(0.f);
		}
		else if (!vanRef || vanRef->IsFreezeTankFull())
		{
			Deactivate();
			SetActionDuration(0.f);
		}
	}
}

void AAntiFreeze::Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	if (interactor && interactComponent && bIsHeld && !bIsComplete)
	{
		userComponent = interactComponent;
		TraceCap(false);		
	}
}

void AAntiFreeze::TraceCap(const bool bOnlyTrace)
{
	FHitResult hitResult;

	if (userComponent->GetTraceResult(180.f, hitResult) && hitResult.GetActor())
	{
		ACoolantCap* hitCap = Cast<ACoolantCap>(hitResult.GetActor());
		if (hitCap && hitCap->bIsOpen)
		{
			if (!bOnlyTrace)
			{
				coolantCap = hitCap;
				vanRef = hitCap->vanRef;
				if (vanRef && !vanRef->IsFreezeTankFull())
				{
					SetActionDuration(amountOfAntiFreeze);
					Activate();
					userComponent->bIsInteracting = true;
					userComponent->NotifyItemSettingsUpdate(this);
				}
			}
		}
		else
		{
			Deactivate();
		}
	}
	else
	{
		Deactivate();
	}
}

void AAntiFreeze::Interrupt_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	Super::Interrupt_Implementation(interactor, interactComponent);

	if (interactComponent)
	{
		if (bIsHeld)
		{
			interactComponent->bIsInteracting = false;
			vanRef = nullptr;
			SetActionDuration(0.f);
		}
	}
}

void AAntiFreeze::Deactivate_Implementation()
{
	Super::Deactivate_Implementation();

	vanRef = nullptr;
	coolantCap = nullptr;
}

void AAntiFreeze::NotifyDrop_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	Super::NotifyDrop_Implementation(interactor, interactComponent);

	vanRef = nullptr;
	actionDuration = 0.f;
}

float AAntiFreeze::GetCurrentAntiFreeze()
{
	float currentAntiFreeze = FMath::Clamp(amountOfAntiFreeze - progress, 0.f, 6.f);
	return currentAntiFreeze;
}

void AAntiFreeze::UpdateWeight()
{
	float currentFuel = GetCurrentAntiFreeze();
	if (currentFuel <= 2.f)
		itemMesh->SetMassOverrideInKg(FName("None"), 20.f, true);
	else if (currentFuel > 2.f)
		itemMesh->SetMassOverrideInKg(FName("None"), 28.f, true);
}
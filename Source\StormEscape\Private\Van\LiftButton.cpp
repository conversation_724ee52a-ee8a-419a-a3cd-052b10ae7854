#include "Van/LiftButton.h"
#include "Van/GameplayVan.h"

ALiftButton::ALiftButton()
{
	bIsRemovable = false;
	bRequiresToolBox = false;
	bIsOn = true;

	progressType = EProgressType::None;
	activationType = EActivationType::Use;
}

void ALiftButton::OpenDoor()
{
	bIsOn = false;
	UpdateButtonMaterial(bIsOn);
	OnLiftButtonPressedDelegate.Broadcast(true);
}

void ALiftButton::Use_Implementation()
{
	Super::Use_Implementation();

	if (vanRef)
	{
		if (bIsOn)
		{
			OpenDoor();
		}
		else if (!bIsOn && vanRef->bVanStarted)
		{
			bIsOn = true;
			UpdateButtonMaterial(bIsOn);
			OnLiftButtonPressedDelegate.Broadcast(false);
		}
	}
}

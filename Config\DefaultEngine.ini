

[/Script/EngineSettings.GameMapsSettings]
GameDefaultMap=/Game/StormEscape/Maps/MainMenu.MainMenu
EditorStartupMap=/Game/StormEscape/Maps/lvl_MechanicsGym.lvl_MechanicsGym
GameInstanceClass=/Game/StormEscape/Core/GameInstance/BP_StormEscape_GI.BP_StormEscape_GI_C
GlobalDefaultGameMode=/Game/StormEscape/Core/GameMode/GM_test.GM_test_C

[Audio]
UnfocusedVolumeMultiplier=1.0

[/Script/Engine.RendererSettings]
r.AllowStaticLighting=True

r.GenerateMeshDistanceFields=True

r.DynamicGlobalIlluminationMethod=0

r.HeterogeneousVolumes.MaxTraceDistance=2000000

r.ReflectionMethod=2

r.SkinCache.CompileShaders=True

r.RayTracing=False

r.Shadow.Virtual.Enable=0

r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange=True

r.DefaultFeature.LocalExposure.HighlightContrastScale=0.8

r.DefaultFeature.LocalExposure.ShadowContrastScale=0.8
r.DefaultFeature.AutoExposure=False
r.Nanite.ProjectEnabled=False
r.Nanite = 0
r.Lumen.HardwareRayTracing=False
r.Lumen.TraceMeshSDFs=0
r.Lumen.Reflections.HardwareRayTracing.Translucent.Refraction.EnableForProject=False
r.PathTracing=False
r.ScreenPercentage.Default.Desktop.Mode=1
r.MSAACount=4
r.VirtualTextures=True
r.VirtualTexturedLightmaps=True
r.EarlyZPass=2
r.EarlyZPassOnlyMaterialMasking=True
r.Shadow.CSMCaching=True
r.AntiAliasingMethod=4
r.ForwardShading=False
r.Mobile.AntiAliasing=0

[/Script/WindowsTargetPlatform.WindowsTargetSettings]
DefaultGraphicsRHI=DefaultGraphicsRHI_DX11
-D3D12TargetedShaderFormats=PCD3D_SM5
+D3D12TargetedShaderFormats=PCD3D_SM5
+D3D12TargetedShaderFormats=PCD3D_SM6
-D3D11TargetedShaderFormats=PCD3D_SM5
+D3D11TargetedShaderFormats=PCD3D_SM5
+VulkanTargetedShaderFormats=SF_VULKAN_SM5
+VulkanTargetedShaderFormats=SF_VULKAN_SM6
Compiler=Default
AudioSampleRate=48000
AudioCallbackBufferFrameSize=1024
AudioNumBuffersToEnqueue=1
AudioMaxChannels=0
AudioNumSourceWorkers=4
SpatializationPlugin=
SourceDataOverridePlugin=
ReverbPlugin=
OcclusionPlugin=
CompressionOverrides=(bOverrideCompressionTimes=False,DurationThreshold=5.000000,MaxNumRandomBranches=0,SoundCueQualityIndex=0)
CacheSizeKB=65536
MaxChunkSizeOverrideKB=0
bResampleForDevice=False
MaxSampleRate=48000.000000
HighSampleRate=32000.000000
MedSampleRate=24000.000000
LowSampleRate=12000.000000
MinSampleRate=8000.000000
CompressionQualityModifier=1.000000
AutoStreamingThreshold=0.000000
SoundCueCookQualityIndex=-1

[/Script/LinuxTargetPlatform.LinuxTargetSettings]
SpatializationPlugin=
SourceDataOverridePlugin=
ReverbPlugin=
OcclusionPlugin=
SoundCueCookQualityIndex=-1
-TargetedRHIs=SF_VULKAN_SM5

[/Script/HardwareTargeting.HardwareTargetingSettings]
TargetedHardwareClass=Desktop
AppliedTargetedHardwareClass=Desktop
DefaultGraphicsPerformance=Maximum
AppliedDefaultGraphicsPerformance=Maximum

[/Script/WorldPartitionEditor.WorldPartitionEditorSettings]
CommandletClass=Class'/Script/UnrealEd.WorldPartitionConvertCommandlet'

[/Script/Engine.UserInterfaceSettings]
bAuthorizeAutomaticWidgetVariableCreation=False
FontDPIPreset=Standard
FontDPI=72

[/Script/Engine.Engine]
+ActiveGameNameRedirects=(OldGameName="TP_Blank",NewGameName="/Script/StormEscape")
+ActiveGameNameRedirects=(OldGameName="/Script/TP_Blank",NewGameName="/Script/StormEscape")
NearClipPlane=2.000000
GameViewportClientClassName=/Script/CommonUI.CommonGameViewportClient

[/Script/AndroidFileServerEditor.AndroidFileServerRuntimeSettings]
bEnablePlugin=True
bAllowNetworkConnection=True
SecurityToken=F8CA02724C24B4D16E1F6AAE18A0F34C
bIncludeInShipping=False
bAllowExternalStartInShipping=False
bCompileAFSProject=False
bUseCompression=False
bLogFiles=False
bReportStats=False
ConnectionType=USBOnly
bUseManualIPAddress=False
ManualIPAddress=

[/Script/Engine.GameEngine]
+NetDriverDefinitions=(DefName="GameNetDriver",DriverClassName="OnlineSubsystemSteam.SteamNetDriver",DriverClassNameFallback="OnlineSubsystemUtils.IpNetDriver")

[OnlineSubsystem]
DefaultPlatformService=Steam

[OnlineSubsystemSteam]
bEnabled=true
SteamDevAppId=3734170
bInitServerOnClient=true

[/Script/OnlineSubsystemSteam.SteamNetDriver]
NetConnectionClassName="OnlineSubsystemSteam.SteamNetConnection"



[CoreRedirects]
+FunctionRedirects=(OldName="/Script/StormEscape.StormEscapeGameInstance.JoinServer",NewName="/Script/StormEscape.StormEscapeGameInstance.SearchForServer")
+FunctionRedirects=(OldName="/Script/StormEscape.StormEscapeGameInstance.HostSession",NewName="/Script/StormEscape.StormEscapeGameInstance.HostLobby")
+FunctionRedirects=(OldName="/Script/StormEscape.StormEscapeGameInstance.HostLobby",NewName="/Script/StormEscape.StormEscapeGameInstance.HostLobbySession")
+FunctionRedirects=(OldName="/Script/StormEscape.LobbyVoiceChatComponent.OnOdinMediaAdded",NewName="/Script/StormEscape.LobbyVoiceChatComponent.OnOthersMediaAdded")
+FunctionRedirects=(OldName="/Script/StormEscape.LobbyVoiceChatComponent.OnOdinMediaRemoved",NewName="/Script/StormEscape.LobbyVoiceChatComponent.OnOthersMediaRemoved")
+FunctionRedirects=(OldName="/Script/StormEscape.LobbyVoiceChatComponent.OnOdinRoomJoinedHandler",NewName="/Script/StormEscape.LobbyVoiceChatComponent.OnJoinedOdinRoom")
+FunctionRedirects=(OldName="/Script/StormEscape.LobbyVoiceChatComponent.OnMediaAddedToRoom",NewName="/Script/StormEscape.LobbyVoiceChatComponent.OnMyMediaAddedToRoom")
+FunctionRedirects=(OldName="/Script/StormEscape.StormEscapeGameInstance.FindSessions",NewName="/Script/StormEscape.StormEscapeGameInstance.SearchForLobbies")
+FunctionRedirects=(OldName="/Script/StormEscape.StormEscapeGameInstance.JoinSession",NewName="/Script/StormEscape.StormEscapeGameInstance.JoinLobby")
+FunctionRedirects=(OldName="/Script/StormEscape.StormEscapeGameInstance.FindCharacterForGuid",NewName="/Script/StormEscape.StormEscapeGameInstance.FindCharacterByGuid")
+FunctionRedirects=(OldName="/Script/StormEscape.LobbyVoiceChatComponent.StartVoiceChat",NewName="/Script/StormEscape.LobbyVoiceChatComponent.ConnectToVoiceChat")
+PropertyRedirects=(OldName="/Script/StormEscape.GameplayVoiceChatComponent.PlayerGuid",NewName="/Script/StormEscape.GameplayVoiceChatComponent.OwnerGuid")
+FunctionRedirects=(OldName="/Script/StormEscape.GameplayVoiceChatComponent.SetVoiceChannelActive",NewName="/Script/StormEscape.GameplayVoiceChatComponent.AddMediaToChannelRoom")


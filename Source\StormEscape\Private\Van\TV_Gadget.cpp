﻿#include "Van/TV_Gadget.h"

#include "CommonActivatableWidget.h"
#include "GameFramework/GameModeBase.h"
#include "Blueprint/UserWidget.h"
#include "BlueprintLibraries/UIHelperLibrary.h"
#include "Kismet/GameplayStatics.h"
#include "Core/Lobby/LobbyGameMode.h"
#include "GameFramework/PlayerController.h"
#include "UI/BaseMainLayerUI.h"
#include "UI/UIManager.h"

void ATV_Gadget::Use_Implementation()
{
	Super::Use_Implementation();

	
	// Get the current game mode
	AGameModeBase* GameMode = UGameplayStatics::GetGameMode(GetWorld());
	if (!GameMode)
	{
		UE_LOG(LogTemp, Warning, TEXT("GameMode is null."));
		return;
	}

	// Determine which widget to display based on the game mode type
	TSubclassOf<UCommonActivatableWidget> WidgetToDisplay = nullptr;

	if (Cast<ALobbyGameMode>(GameMode))
	{
		WidgetToDisplay = LobbyWidgetClass;
	}
	else
	{
		WidgetToDisplay = MainMenuWidgetClass;
	}

	ClientShowWidget(WidgetToDisplay);
}

void ATV_Gadget::ClientShowWidget_Implementation(TSubclassOf<UCommonActivatableWidget> WidgetToDisplay)
{
	if (!WidgetToDisplay)
	{
		UE_LOG(LogTemp, Warning, TEXT("Widget class is not set."));
		return;
	}

	// Fire-and-forget style widget creation using UIManager
	UCommonActivatableWidget* Widget = UUIManager::AddWidgetToStack(
		this,
		WidgetToDisplay,
		EWidgetLayer::Menu,              
		EInputModeType::UIOnly,           
		true,                             
		false                            
	);

	if (!Widget)
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to create widget."));
		return;
	}

	// Optional: bind to destruction to trigger logic when widget closes
	Widget->OnNativeDestruct.AddUFunction(this, FName("OnWidgetClosed"));
}

void ATV_Gadget::OnWidgetClosed(UCommonActivatableWidget* DestroyedWidget)
{
	// Call the Blueprint event
	K2_OnWidgetClosed();

	// Perform additional actions, such as playing effects
	UE_LOG(LogTemp, Log, TEXT("Widget closed. Playing effect..."));
}

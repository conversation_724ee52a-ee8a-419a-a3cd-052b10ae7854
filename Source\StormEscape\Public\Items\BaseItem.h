#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Interfaces/InteractionInterface.h"
#include "InputMappingContext.h"
#include "EnhancedInputComponent.h"
#include "BaseItem.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogItem, Log, All);

DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FOnItemProgressChanged, AActor*, item, float, progressTime, float, duration);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnItemCarrierChanged, AActor*, carrier);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnActionStarted);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnActionCompleted);

class USphereComponent;

UENUM(BlueprintType)
enum class EActivationType : uint8
{
	None = 0	UMETA(DisplayName = "None"),
	Use = 1		UMETA(DisplayName = "Use"),
	Toggle = 2  UMETA(DisplayName = "Toggle"),
	Hold = 3    UMETA(DisplayName = "Hold")
};

UENUM(BlueprintType)
enum class EProgressType : uint8
{
	None = 0		UMETA(DisplayName = "None"),
	Activation = 1  UMETA(DisplayName = "Activation"),
	Persistent = 2	UMETA(DisplayName = "Persistent")
};

UCLASS()
class STORMESCAPE_API ABaseItem : public AActor, public IInteractionInterface
{
	GENERATED_BODY()
	
public:	
	ABaseItem();

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

protected:
	virtual void BeginPlay() override;

/*INTERACTION */
public:
	/**
	* Called by the player's interact component (server only) when the player presses the interact/use button.
	* Unless you need a custom activation logic, please override Activate() to implement the item's funcionality instead. 
	*/
	void Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	/**
	* Called by the player's interact component (server only) when the player releases the interact/use button.
	* Unless you need a custom deactivation logic, please override Deactivate() to implement the item's funcionality instead.
	*/
	void Interrupt_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	/**
	* Called by the player's interact component (server only) before the player presses the interact/use button.
	* Unless you need a custom activation logic, please override Activate() to implement the item's funcionality instead.
	*/
	void NotifyStartAction_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	/**
	* Called by the player's interact component (server only) before the player releases the interact/use button.
	*/
	void NotifyStopAction_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	/**
	* Called by the player's interact component (server only) when the player picks the item up. 
	* Requires bIsPickable = true.
	*/
	void NotifyPickUp_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	/**
	* Called by the player's interact component (server only) when the player drops the item.
	* Requires bIsPickable = true.
	*/
	void NotifyDrop_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	//Called by the player's interact component (server only) when the item enters a player's interaction range.
	void NotifyFocus_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	//Called by the player's interact component (server only) when the item leaves a player's interaction range.
	void NotifyUnfocus_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	//Returns true if the item has a progress bar
	FORCEINLINE bool CanShowProgress_Implementation(AActor* otherActor)  const override { return progressType > EProgressType::None && actionDuration > 0.f; }

	//Returns the time the item needs to progress to complete its action
	FORCEINLINE float GetActionDuration_Implementation() override { return actionDuration; }

	//Returns the accumulated progress time
	FORCEINLINE float GetCurrentProgress_Implementation() override { return progress; }

	//Returns the percentual progress
	FORCEINLINE float GetCurrentProgressNormalized_Implementation() override { return actionDuration > 0.f ? progress/actionDuration : 1.f; }

	FORCEINLINE bool IsAPickeableItem_Implementation() override { return bIsPickable; }

	FORCEINLINE bool CanBeInteracted_Implementation() override { return !bIsHeld; }

	FORCEINLINE bool IsHeld_Implementation() override { return bIsHeld; }

	FORCEINLINE UStaticMeshComponent* GetItemMesh_Implementation() override { return itemMesh; }

	FORCEINLINE bool RequiresTool_Implementation() override { return false; }

	FOnItemCarrierChanged OnCarrierChangedDelegate;

protected:
	UFUNCTION(BlueprintCallable)
	virtual AActor* GetDefaultOwner() const { return nullptr; }

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UStaticMeshComponent* itemMesh;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	USphereComponent* itemCollision;

	// If true, the interact button picks the item up, and the Use item button triggers the interaction events instead.
	UPROPERTY(EditAnywhere, Replicated, BlueprintReadOnly, Category = "Properties");
	bool bIsPickable;

	UFUNCTION()
	virtual void OnRep_IsHeldUpdate();

	// Whether the item is being carried by a player
	UPROPERTY(ReplicatedUsing = OnRep_IsHeldUpdate, BlueprintReadWrite)
	bool bIsHeld = false;

	UFUNCTION()
	virtual void OnRep_IsFocusedUpdate();

	UPROPERTY(Replicated)
	UInteractComponent* userComponent;

	//Whether the item is within a player's interaction range
	UPROPERTY(ReplicatedUsing = OnRep_IsFocusedUpdate, BlueprintReadWrite)
	bool bIsFocused = false;

	// Blueprint event called on all players when the item is picked up.
	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnPickedUp")
	void K2_OnPickedUp();

	// Blueprint event called on all players when the item is dropped.
	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnDropped")
	void K2_OnDropped();

	// Blueprint event called on all players when the item enters a player's interaction range
	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnFocused")
	void K2_OnFocused();

	// Blueprint event called on all players when the item leaves a player's interaction range
	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnUnfocused")
	void K2_OnUnfocused();

/* ACTIVATION */
public:
	//Use the item, provided that CanActivate() = true and activationType = Use.
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void TryUse();

	//Turn the item on, provided that CanActivate() = true and activationType = Toggle/Hold.
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void TryActivate();

	//Turn the item off.
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void TryDeactivate();

protected:
	// Returns whether the item can be used
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	bool CanUse() const;

	// Returns whether the item can be activated
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	bool CanActivate() const;

	// Returns whether the item can be focused on
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	bool CanFocus() const;

	/**
	* Called by TryUse() (server only) when the item can be used.
	* Override this to implement the item's "instant" logic.
	* Please call TryUse() to manually use the item, rather than calling Use() directly.
	*/
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void Use();

	/**
	* Called by TryUse() (server only), after the item was used to reset ownership
	* Override this to close or clean anything that must be handled after Use().
	*/
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void CleanUse();

	/**
	* Called by TryActivate() (server only) when the item can be activated.
	* Override this to implement the item's "start" logic.
	* Please call TryActivate() to manually activate the item, rather than calling Activate() directly.
	*/
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void Activate();
 
	/**
	* Called by the update timer in loop (server only) while the item is activated.
	* Override this to implement the item's "ON" logic.
	*/
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void Update(float deltaSeconds);

	/**
	* Called by TryDeactivate() (server only) when the item can be deactivated.
	* Override this to implement the item's "stop" logic.
	* Please call TryDeactivate() to manually use the item, rather than calling Deactivate() directly.
	*/
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void Deactivate();

	/**
	* How the item handles the Interact() and Interrupt() events.
	* USE: Doesn't activate/deactivate. Instead, it calls Use() when interacted with.
	* TOGGLE: Interact once to activate, do it again to deactivate.
	* HOLD: Press interact to activate, release it to deactivate.
	*/
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Activation")
	EActivationType activationType;

	// Whether the item should try to activate at BeginPlay().
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Activation")
	bool bAutoActivate;

	// If true, the item can't be activated again once it activates for the first time.
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Activation")
	bool bActivatesOnce;

	FTimerHandle updateTimer;

	float updateFreq = 0.05f;

	UFUNCTION(NetMulticast, Reliable)
	void Multicast_OnUsed();

	UFUNCTION()
	virtual void OnRep_ActivatedUpdate();

	// Whether the item is currently on
	UPROPERTY(ReplicatedUsing = OnRep_ActivatedUpdate, BlueprintReadWrite)
	bool bActivated = false;

	// Blueprint event called on all players when the item is used
	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnUsed")
	void K2_OnUsed();

	// Blueprint event called on all players when the item is activated
	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnActivated")
	void K2_OnActivated();

	// Blueprint event called on all players when the item is deactivated
	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnDeactivated")
	void K2_OnDeactivated();

/* PROGRESS */
public:
	// C++ event called on all players when the item's progress changes
	FOnItemProgressChanged OnProgressChangedDelegate;

	// C++ event called when the item's progress starts.
	FOnActionStarted OnActionStartedDelegate;

	// C++ event called on the server when the item's progress is complete
	FOnActionCompleted OnActionCompletedDelegate;

protected:
	/**
	* Called by the process timer in loop (server only) while the item checking progress.
	* Override this to implement custom progress logic.
	*/
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void Process(float deltaSeconds);

	/**
	* Called by Process() to determine whether the item is progressing.
	* Returns true if progressType != None.
	* Override this to implement custom progress requirements.
	*/
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	bool ShouldProgress();

	/**
	* Called by Process() when enough progress is accumulated.
	* Override this to implement the item's "delayed" action.
	*/
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void Complete();

	/**
	* Called by Activate() when the item activates with complete progress, provided that bActivatesOnce = false.
	* Override this to implement the item's "reboot" action.
	*/
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void RestartProgress();

	FTimerHandle processTimer;

	UFUNCTION()
	virtual void OnRep_IsCompleteUpdate();

	// Whether the item has finished progressing
	UPROPERTY(ReplicatedUsing = OnRep_IsCompleteUpdate, BlueprintReadWrite)
	bool bIsComplete = false;

	UFUNCTION()
	virtual void OnRep_ProgressUpdate();

	// The accumulated time towards the action completion.
	UPROPERTY(ReplicatedUsing = OnRep_ProgressUpdate, BlueprintReadWrite)
	float progress;

	/**
	* How the item progresses towards completion.
	* NONE: Doesn't progress.
	* ACTIVATION: Progress while activating. Deactivating the item will reset the progress to 0.
	* PERSISTENT: Progress while activating. Doesn't reset when deactivated.
	*/
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Progress")
	EProgressType progressType;

	void SetActionDuration(float newDuration);

	UFUNCTION()
	virtual void OnRep_ActionDurationChanged();

	// The amount of time the item needs to progress in order to execute its completion logic.
	UPROPERTY(EditAnywhere, ReplicatedUsing=OnRep_ActionDurationChanged, BlueprintReadOnly, Category = "Properties|Progress")
	float actionDuration;

	// Blueprint event called on all players when the item's progress change.
	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnProgressChanged")
	void K2_OnProgressChanged(float current, float goal);

	// Blueprint event called on all players when the item executes its completion logic.
	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnProgressComplete")
	void K2_OnProgressCompleted();

	// Blueprint event called on all players when the item's progress is reset after completion.
	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnProgressReset")
	void K2_OnProgressReset();
};

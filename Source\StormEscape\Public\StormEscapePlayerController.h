// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "InputAction.h"
#include "GameFramework/PlayerController.h"
#include "Interfaces/ControllerInterface.h"
#include "Settings/CustomUISettings.h"
#include "UI/StormEscapeHUD.h"
#include "StormEscapePlayerController.generated.h"

class UBaseMainLayerUI;
class UCommonActivatableWidget;
class UGameplayVoiceChatComponent;
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPlayerDeath, APlayerController*, Player);

/**
 * 
 */
UCLASS(Blueprintable)
class STORMESCAPE_API AStormEscapePlayerController : public APlayerController , public IControllerInterface
{
	GENERATED_BODY()
	
public:

	AStormEscapePlayerController();

	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	UGameplayVoiceChatComponent* VoiceChatComp;

	UPROPERTY(EditAnywhere, Category = "Input")
	UInputAction* PauseAction;

	UPROPERTY(EditAnywhere, Category = "UI")
	TSubclassOf<UBaseMainLayerUI> MainLayerUIClass;
	
	UPROPERTY(BlueprintReadOnly, Category = "UI")
	UBaseMainLayerUI* MainLayerUI;

	UFUNCTION(BlueprintCallable)
	void TogglePauseMenu();


	void NotifyTornadoHit(AActor* tornado);

	FOnPlayerDeath OnPlayerDeathDelegate;
	
protected:
	
	virtual void BeginPlay() override;

	virtual void SetupInputComponent() override;
	
	virtual void ShowErrorPopup_Implementation(const FString& ErrorHead, const FString& ErrorBody) override;
	
	virtual void KickToMainMenu_Implementation(const FString& Reason) override;

	UFUNCTION(Client, Reliable)
	void ClientShowErrorPopup(const FString& ErrorHead, const FString& ErrorBody);

	UFUNCTION(Client, Reliable)
	void Client_KickToMainMenu(const FString& String);

	//Add a client rpc to show the loading screen
	UFUNCTION(Client, Reliable)
	void ClientShowLoadingScreen();

private:
	AStormEscapeHUD* GameHUD;

	TSubclassOf<UCommonActivatableWidget> PauseMenuClass;

};


#include "Items/Tools/ScissorJack.h"
#include "Camera/CameraComponent.h"
#include "Components/InteractComponent.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Interfaces/RepairInterface.h"
#include "Van/GameplayVan.h"
#include "Net/UnrealNetwork.h"

AScissorJack::AScissorJack()
{
	bIsPickable = true;
	itemMesh->SetSimulatePhysics(true);
	actionDuration = 0.f;
	bIsPlaced = false;
	timeToLiftVan = 7.f;
	vehiclePart = EVehiclePart::Scissor_Jack;
	bCanBeRemoved = true;
	bRequieresToolBox = false;
}

void AScissorJack::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(AScissorJack, bCanBeRemoved);
	DOREPLIFETIME(AScissorJack, bRequieresToolBox);
	DOREPLIFETIME(AScissorJack, bIsPlaced);
	//DOREPLIFETIME(AScissorJack, interactComponentRef);
}

void AScissorJack::Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	if (interactor && interactComponent)
	{
		userComponent = interactComponent;
		if (bIsHeld)
		{
			TraceVehicleParts(userComponent);
		}
		else
		{
			Super::Interact_Implementation(interactor, interactComponent);
		}
	}
}

void AScissorJack::ApplyTransparency_Implementation(bool bShouldApply)
{
}

void AScissorJack::TraceVehicleParts(UInteractComponent* interactComponent)
{
	FHitResult hitResult;

	if (interactComponent->GetTraceResult(180.f, hitResult))
	{
		if (hitResult.GetActor())
		{
			ATire* tire = Cast<ATire>(hitResult.GetActor());
			if (tire && tire->vehiclePart == EVehiclePart::Tire && tire->bIsDamaged && !IRepairInterface::Execute_HasJackInstalled(tire))
			{
				interactComponent->DropItem(false);
				InstallJack(tire);
				if (tire->vanRef)
				{
					OnProgressChangedDelegate.AddDynamic(tire->vanRef, &AGameplayVan::LiftVan);
					tire->vanRef->OnTireInstalledDelagate.AddDynamic(this, &AScissorJack::HandleTireInstalled);
					tire->vanRef->HandleJackInserted(tire->Tags[0]);
				}
			}
		}
	}
}

void AScissorJack::InstallJack(ATire* tire)
{
	itemMesh->SetSimulatePhysics(false);
	itemMesh->SetCollisionResponseToAllChannels(ECR_Ignore);
	itemMesh->SetCollisionResponseToChannel(ECC_Visibility, ECR_Block);
	FVector_NetQuantize targetLocation = tire->GetActorLocation() + placementOffset;
	FRotator targetRotation = tire->GetActorRotation();
	SetActorLocationAndRotation(targetLocation, targetRotation);

	bIsPickable = false;
	bIsPlaced = true;
	bRequieresToolBox = true;
	bCanBeRemoved = false;
	SetActionDuration(timeToLiftVan);
	ApplyTransparency(true);
	raisedTire = tire;
}

void AScissorJack::HandleTireInstalled()
{
	bCanBeRemoved = true;
	bRequieresToolBox = false;
	SetActionDuration(3.f);
	progress = 0.f;
	OnRep_ProgressUpdate();
}

void AScissorJack::Complete_Implementation()
{
	if (!bIsHeld && bIsPlaced && userComponent)
	{
		if (bCanBeRemoved)
		{
			if (raisedTire && raisedTire->vanRef)
			{
				raisedTire->vanRef->HandleJackRemoved();
				raisedTire->vanRef->OnTireInstalledDelagate.RemoveAll(this);
				SetActionDuration(0.f);
			}

			progress = 0.f;
			OnRep_ProgressUpdate();

			if (userComponent->IsHoldingItem())
				userComponent->DropItem(false);

			itemMesh->SetSimulatePhysics(true);
			userComponent->PerformGrab(this);
			bIsPickable = true;
		}
		else
		{
			ApplyTransparency(false);
			if (raisedTire)
			{
				raisedTire->bCanBeRemoved = true;
				OnProgressChangedDelegate.RemoveAll(raisedTire->vanRef);
			}
		}
	}

	OnActionCompletedDelegate.Broadcast();
	GetWorld()->GetTimerManager().ClearTimer(processTimer);
}

#pragma once

#include "CoreMinimal.h"
#include "Van/BaseVehiclePartSpot.h"
#include "BatterySpot.generated.h"

class ABattery;
class ABatteryTerminal;

UCLASS()
class STORMESCAPE_API ABatterySpot : public ABaseVehiclePartSpot
{
	GENERATED_BODY()
	
public:
	ABatterySpot();

	void SetBatteryReference(ABattery* battery);

protected:
	virtual void BeginPlay() override;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UChildActorComponent* redTerminal;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UChildActorComponent* blackTerminal;

	UPROPERTY(BlueprintReadWrite)
	bool bRedTerminalInstalled;

	UPROPERTY(BlueprintReadWrite)
	bool bBlackTerminalInstalled;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Properties|Repairing")
	FTransform redLooseTransform;

	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Properties|Repairing")
	FTransform blackLooseTransform;

private:
	UStaticMeshComponent* interactedTerminal;
	ABattery* batteryRef;
	ABatteryTerminal* redTerminalRef;
	ABatteryTerminal* blackTerminalRef;

	FTransform redTransform;
	FTransform blackTransform;

	UFUNCTION()
	void HandleRedTerminalInteraction(bool bLoose);

	UFUNCTION()
	void HandleBlackTerminalInteraction(bool bLoose);

	UFUNCTION(NetMulticast, Reliable)
	void Multicast_ToggleTerminal(UChildActorComponent* terminal, FTransform transform);

	void CheckTerminals();
};

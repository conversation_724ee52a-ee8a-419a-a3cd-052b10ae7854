// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "CharacterAttributesComponent.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnHealthUpdated, float, health, float, maxHealth);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnStaminaUpdated, float, stamina, float, maxStamina);

UCLASS( ClassGroup=(Custom), meta=(BlueprintSpawnableComponent) )
class STORMESCAPE_API UCharacterAttributesComponent : public UActorComponent
{
	GENERATED_BODY()

public:	
	UCharacterAttributesComponent();

	virtual void TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction) override;

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	UFUNCTION()
	void OnRep_HealthChanged();

	UPROPERTY(ReplicatedUsing=OnRep_HealthChanged, BlueprintReadOnly)
	float health;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Attributes | Health")
	float maxHealth;

	UFUNCTION()
	void OnRep_StaminaChanged();

	UPROPERTY(ReplicatedUsing=OnRep_StaminaChanged, BlueprintReadOnly)
	float stamina;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Attributes | Speed")
	float walkingSpeed;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Attributes | Speed")
	float runningSpeed;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Attributes | Speed")
	float crouchingSpeed;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Attributes | Speed")
	float hardLandThreshold;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Attributes | Stamina")
	float maxStamina;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Attributes | Stamina")
	float staminaDrainRate;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Attributes | Stamina")
	float staminaRecoveryRate;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Attributes | Stamina")
	float minimumStaminaToRun;

	UPROPERTY(BlueprintAssignable)
	FOnHealthUpdated OnHealthUpdatedDelgate;

	UPROPERTY(BlueprintAssignable)
	FOnStaminaUpdated OnStaminaUpdatedDelgate;

	UFUNCTION(BlueprintCallable, BlueprintPure)
	FORCEINLINE bool HasEnoughStamina() { return stamina > minimumStaminaToRun; }

protected:
	virtual void BeginPlay() override;
};

#include "Van/Oil.h"
#include "Van/AntiFreeze.h"
#include "Camera/CameraComponent.h"
#include "Components/InteractComponent.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Van/GameplayVan.h"
#include "Van/OilCap.h"

AOil::AOil()
{
	bIsPickable = true;
	itemMesh->SetSimulatePhysics(true);
	amountOfOil = 6.f;
	progressType = EProgressType::Persistent;
	bActivatesOnce = true;
}

void AOil::BeginPlay()
{
	Super::BeginPlay();

	UpdateWeight();
	UpdateWidget(GetCurrentOil());
}

void AOil::Process_Implementation(float deltaSeconds)
{
	if (progressType > EProgressType::None && ShouldProgress() && vanRef && oilCap)
	{
		progress += deltaSeconds;
		OnRep_ProgressUpdate();

		vanRef->FillOilTank(deltaSeconds);
		UpdateWeight();
		UpdateWidget(GetCurrentOil());

		TraceCap(true);

		if (progress >= actionDuration)
		{
			Complete();
			vanRef = nullptr;
			oilCap = nullptr;
			SetActionDuration(0.f);
		}
		else if (!vanRef || vanRef->IsFreezeTankFull())
		{
			Deactivate();
			SetActionDuration(0.f);
		}
	}
}

void AOil::Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	if (interactor && interactComponent && bIsHeld && !bIsComplete)
	{
		userComponent = interactComponent;
		TraceCap(false);
	}
}

void AOil::TraceCap(const bool bOnlyTrace)
{
	FHitResult hitResult;

	if (userComponent->GetTraceResult(180.f, hitResult) && hitResult.GetActor())
	{
		AOilCap* hitCap = Cast<AOilCap>(hitResult.GetActor());
		if (hitCap && hitCap->bIsOpen)
		{
			if (!bOnlyTrace)
			{
				oilCap = hitCap;
				vanRef = hitCap->vanRef;
				if (vanRef && !vanRef->IsOilTankFull())
				{
					SetActionDuration(amountOfOil);
					Activate();
					userComponent->bIsInteracting = true;
					userComponent->NotifyItemSettingsUpdate(this);
				}
			}
		}
		else
		{
			Deactivate();
		}
	}
	else
	{
		Deactivate();
	}
}

void AOil::Interrupt_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	Super::Interrupt_Implementation(interactor, interactComponent);

	if (interactComponent)
	{
		if (bIsHeld)
		{
			interactComponent->bIsInteracting = false;
			vanRef = nullptr;
			SetActionDuration(0.f);
		}
	}
}

void AOil::Deactivate_Implementation()
{
	Super::Deactivate_Implementation();

	vanRef = nullptr;
	oilCap = nullptr;
}

void AOil::NotifyDrop_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	Super::NotifyDrop_Implementation(interactor, interactComponent);

	vanRef = nullptr;
	actionDuration = 0.f;
}

float AOil::GetCurrentOil()
{
	float currentOil = FMath::Clamp(amountOfOil - progress, 0.f, 6.f);
	return currentOil;
}

void AOil::UpdateWeight()
{
	float currentFuel = GetCurrentOil();
	if (currentFuel <= 2.f)
		itemMesh->SetMassOverrideInKg(FName("None"), 20.f, true);
	else if (currentFuel > 2.f)
		itemMesh->SetMassOverrideInKg(FName("None"), 28.f, true);
}

﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "UI/Lobby/CrewMembersList.h"

#include "Core/Lobby/LobbyGameState.h"

void UCrewMembersList::NativeConstruct()
{
	Super::NativeConstruct();

	if (ALobbyGameState* GS = GetWorld()->GetGameState<ALobbyGameState>())
	{
		if (!GS->OnPlayerListChanged.IsAlreadyBound(this,&UCrewMembersList::UpdatePlayerList))
		{
			UpdatePlayerList(GS->PlayerInfoList);
			GS->OnPlayerListChanged.AddDynamic(this, &UCrewMembersList::UpdatePlayerList);
		}
	}
}

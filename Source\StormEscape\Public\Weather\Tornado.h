// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/CapsuleComponent.h"
#include "Tornado.generated.h"

UCLASS()
class STORMESCAPE_API ATornado : public AActor
{
	GENERATED_BODY()
	
public:	
	// Sets default values for this actor's properties
	ATornado();

	virtual void PostInitializeComponents() override;

	virtual void Tick(float DeltaTime) override;

	float GetSpawnDistance() const;

	void StartMovement(FVector destiny);

protected:
	UPROPERTY(EditDefaultsOnly, Category = "Settings", meta = (ClampMin = "0", UIMin = "0", ForceUnits = "km"))
	float minDistance = 5.f;

	UPROPERTY(EditDefaultsOnly, Category = "Settings", meta = (ClampMin = "0", UIMin = "0", ForceUnits = "km"))
	float maxDistance = 10.f;

	UPROPERTY(EditDefaultsOnly, Category = "Settings", meta = (ClampMin = "0", UIMin = "0", ForceUnits = "min"))
	float arrivalTime = 1.f;

private:
	UFUNCTION()
	void OnActorCollision(AActor* SelfActor, AActor* OtherActor);

	UPROPERTY(Category = Character, VisibleAnywhere, BlueprintReadOnly, meta = (AllowPrivateAccess = "true"))
	TObjectPtr<UCapsuleComponent> CapsuleComponent;

	FVector direction;

	double speed;

	bool bIsMoving;
};

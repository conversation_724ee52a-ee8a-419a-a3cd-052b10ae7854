// AnimatableRetainerBox.cpp
#include "UI/AnimatableRetainerBox.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Materials/Material.h"
#include "Engine/Texture.h"

UAnimatableRetainerBox::UAnimatableRetainerBox()
    : Super()
    , bDebugMode(false)
    , MaterialInstance(nullptr)
{
    // Initialize the effect brush
    EffectBrush = FSlateBrush();
    EffectBrush.DrawAs = ESlateBrushDrawType::Box;
    EffectBrush.Tiling = ESlateBrushTileType::NoTile;
}

TSharedRef<SWidget> UAnimatableRetainerBox::RebuildWidget()
{
    TSharedRef<SWidget> Widget = Super::RebuildWidget();
    InitializeMaterialInstance();
    return Widget;
}

void UAnimatableRetainerBox::SynchronizeProperties()
{
    Super::SynchronizeProperties();

    // Sync the brush with the current material
    SyncBrushWithMaterial();

    if (IsDesignTime())
    {
        LogDebug(TEXT("Widget is in design mode"));
    }
}

void UAnimatableRetainerBox::InitializeMaterialInstance()
{
    LogDebug(TEXT("Starting InitializeMaterialInstance"));
    
    // If we already have a valid material instance with valid parent, keep it
    if (MaterialInstance && MaterialInstance->Parent)
    {
        LogDebug(FString::Printf(TEXT("Keeping existing valid MaterialInstance with parent: %s"), 
            *MaterialInstance->Parent->GetName()));
        return;
    }

    // Get material either from brush or effect material
    UMaterialInterface* BrushMaterial = EffectBrush.GetResourceObject() ? 
        Cast<UMaterialInterface>(EffectBrush.GetResourceObject()) : GetEffectMaterial();

    if (!BrushMaterial)
    {
        LogDebug(TEXT("No valid material found in brush or effect material"));
        return;
    }

    LogDebug(FString::Printf(TEXT("Found source material: %s"), *BrushMaterial->GetName()));

    // Store the original material for validation
    UMaterialInterface* OriginalMaterial = BrushMaterial;

    if (UMaterial* BaseMaterial = BrushMaterial->GetMaterial())
    {
        LogDebug(FString::Printf(TEXT("Base material found: %s"), *BaseMaterial->GetName()));
        
        // Create new material instance only if we don't have one or if the parent changed
        if (!MaterialInstance || MaterialInstance->Parent != OriginalMaterial)
        {
            LogDebug(TEXT("Creating new material instance..."));
            UMaterialInstanceDynamic* NewInstance = UMaterialInstanceDynamic::Create(OriginalMaterial, this);
            
            if (NewInstance && NewInstance->Parent)
            {
                MaterialInstance = NewInstance;
                SetEffectMaterial(MaterialInstance);
                SyncBrushWithMaterial();
                
                LogDebug(FString::Printf(TEXT("Successfully created MaterialInstance with parent: %s"), 
                    *MaterialInstance->Parent->GetName()));
                
                // Immediately validate the creation
                if (MaterialInstance->Parent != OriginalMaterial)
                {
                    LogDebug(TEXT("WARNING: Material parent mismatch after creation!"));
                }
            }
            else
            {
                LogDebug(TEXT("Failed to create valid material instance with parent"));
            }
        }
    }
    else
    {
        LogDebug(TEXT("Failed to get base material"));
    }
}

void UAnimatableRetainerBox::SyncBrushWithMaterial()
{
    LogDebug(TEXT("Starting SyncBrushWithMaterial"));
    
    if (!MaterialInstance)
    {
        LogDebug(TEXT("No MaterialInstance to sync"));
        return;
    }

    if (!MaterialInstance->Parent)
    {
        LogDebug(TEXT("WARNING: MaterialInstance has no parent!"));
        return;
    }

    // Only update if necessary
    if (EffectBrush.GetResourceObject() != MaterialInstance)
    {
        LogDebug(FString::Printf(TEXT("Updating brush with MaterialInstance (Parent: %s)"), 
            *MaterialInstance->Parent->GetName()));
            
        EffectBrush.SetResourceObject(MaterialInstance);
        SetEffectMaterial(MaterialInstance);
    }
}

void UAnimatableRetainerBox::OnBrushResourceChanged()
{
    LogDebug(TEXT("Starting OnBrushResourceChanged"));
    
    UMaterialInterface* BrushMaterial = Cast<UMaterialInterface>(EffectBrush.GetResourceObject());
    if (!BrushMaterial)
    {
        LogDebug(TEXT("No valid material in brush"));
        return;
    }

    LogDebug(FString::Printf(TEXT("Brush material changed to: %s"), *BrushMaterial->GetName()));

    // If we already have a valid instance with the same parent, keep it
    if (MaterialInstance && MaterialInstance->Parent && MaterialInstance->Parent == BrushMaterial)
    {
        LogDebug(TEXT("Keeping existing material instance - same parent"));
        return;
    }

    // Store the original material for validation
    UMaterialInterface* OriginalMaterial = BrushMaterial;
    
    // Create new material instance
    UMaterialInstanceDynamic* NewInstance = UMaterialInstanceDynamic::Create(OriginalMaterial, this);
    if (NewInstance && NewInstance->Parent)
    {
        MaterialInstance = NewInstance;
        SetEffectMaterial(MaterialInstance);
        
        LogDebug(FString::Printf(TEXT("Created new MaterialInstance with parent: %s"), 
            *MaterialInstance->Parent->GetName()));
        
        // Re-apply parameters
        UpdateParameters();
    }
    else
    {
        LogDebug(TEXT("Failed to create valid material instance with parent"));
    }
}

void UAnimatableRetainerBox::UpdateParameters()
{
    if (!MaterialInstance)
    {
        return;
    }

    for (const FRetainerBoxMaterialParam& Param : MaterialParams)
    {
        if (!Param.Name.IsNone())
        {
            MaterialInstance->SetScalarParameterValue(Param.Name, Param.CurrentValue);
        }
    }
}

void UAnimatableRetainerBox::AddParameter(FName ParamName, float DefaultValue)
{
    if (ParamName.IsNone())
    {
        return;
    }

    // Check if parameter already exists
    for (const FRetainerBoxMaterialParam& Param : MaterialParams)
    {
        if (Param.Name == ParamName)
        {
            return;
        }
    }

    MaterialParams.Add(FRetainerBoxMaterialParam(ParamName, DefaultValue));
    
    if (MaterialInstance)
    {
        MaterialInstance->SetScalarParameterValue(ParamName, DefaultValue);
    }
}

TArray<FName> UAnimatableRetainerBox::GetAvailableParameters() const
{
    TArray<FName> Result;
    
    if (GetEffectMaterial())
    {
        TArray<FMaterialParameterInfo> ParameterInfos;
        TArray<FGuid> ParameterGuids;
        GetEffectMaterial()->GetAllScalarParameterInfo(ParameterInfos, ParameterGuids);

        for (const FMaterialParameterInfo& ParamInfo : ParameterInfos)
        {
            Result.Add(ParamInfo.Name);
        }
    }
    
    return Result;
}

UMaterialInstanceDynamic* UAnimatableRetainerBox::GetMaterialInstance() const
{
    return MaterialInstance;
}

void UAnimatableRetainerBox::RefreshMaterial()
{
    LogDebug(TEXT("Refreshing material"));
    MaterialInstance = nullptr;
    InitializeMaterialInstance();
}

void UAnimatableRetainerBox::LogDebug(const FString& Message) const
{
    if (bDebugMode)
    {
        UE_LOG(LogTemp, Log, TEXT("[AnimatableRetainerBox] %s"), *Message);
    }
} 
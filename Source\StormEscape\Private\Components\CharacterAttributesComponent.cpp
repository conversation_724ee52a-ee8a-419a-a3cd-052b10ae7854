#include "Components/CharacterAttributesComponent.h"
#include "Net/UnrealNetwork.h"

UCharacterAttributesComponent::UCharacterAttributesComponent()
{
	PrimaryComponentTick.bCanEverTick = false;
	SetIsReplicatedByDefault(true);

	walkingSpeed = 200.f;
	crouchingSpeed = 100.f;
	runningSpeed = 500.f;
	hardLandThreshold = 750.f;
	maxStamina = 100.f;
	staminaDrainRate = 0.25f;
	staminaRecoveryRate = 0.1f;
	minimumStaminaToRun = 20.f;
	maxHealth = 100.f;
}


void UCharacterAttributesComponent::BeginPlay()
{
	Super::BeginPlay();

	stamina = maxStamina;
	health = maxHealth;
}


void UCharacterAttributesComponent::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
	Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
}

void UCharacterAttributesComponent::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	DOREPLIFETIME_CONDITION(UCharacterAttributesComponent, stamina, COND_OwnerOnly);
	DOREPLIFETIME_CONDITION(UCharacterAttributesComponent, health, COND_OwnerOnly);
}

void UCharacterAttributesComponent::OnRep_HealthChanged()
{
	OnHealthUpdatedDelgate.Broadcast(health, maxHealth);
}

void UCharacterAttributesComponent::OnRep_StaminaChanged()
{
	OnStaminaUpdatedDelgate.Broadcast(stamina, maxStamina);
}


#include "Items/Tools/BaseToolbox.h"
#include "Camera/CameraComponent.h"
#include "Components/InteractComponent.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Interfaces/RepairInterface.h"
#include "Van/BaseVehiclePartSpot.h"

ABaseToolbox::ABaseToolbox()
{
	bIsPickable = true;
	itemMesh->SetSimulatePhysics(true);
}

void ABaseToolbox::Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	Super::Interact_Implementation(interactor, interactComponent);

	if (interactor && interactComponent)
	{
		TraceVehicleParts(interactComponent);
	}
}

void ABaseToolbox::TraceVehicleParts(UInteractComponent* interactComponent)
{
	FHitResult hitResult;

	if (interactComponent->GetTraceResult(180.f, hitResult))
	{
		if (hitResult.GetActor())
		{
			AActor* hitActor = hitResult.GetActor();
			if (hitActor->GetClass()->ImplementsInterface(URepairInterface::StaticClass()))
			{
				if (!Cast<ABaseVehiclePartSpot>(hitActor))
				{
					interactComponent->StartInteraction(true);
				}
			}
		}
	}
}

void ABaseToolbox::Interrupt_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	Super::Interrupt_Implementation(interactor, interactComponent);

	if (interactor && interactComponent)
	{
		if (bIsHeld)
		{
			interactComponent->StopInteraction();
		}
	}
}

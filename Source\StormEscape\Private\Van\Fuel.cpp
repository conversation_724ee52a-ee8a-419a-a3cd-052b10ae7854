#include "Van/Fuel.h"
#include "Camera/CameraComponent.h"
#include "Components/InteractComponent.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Van/GameplayVan.h"
#include "Van/FuelCap.h"

AFuel::AFuel()
{
	bIsPickable = true;
	itemMesh->SetSimulatePhysics(true);
	amountOfFuel = 8.f;
	progressType = EProgressType::Persistent;
	bActivatesOnce = true;
	fuelUsed = 0.f;
}

void AFuel::BeginPlay()
{
	Super::BeginPlay();

	UpdateWeight();
	UpdateWidget(GetCurrentFuel());
}

void AFuel::Process_Implementation(float deltaSeconds)
{
	if (progressType > EProgressType::None && ShouldProgress() && vanRef)
	{
		progress += deltaSeconds;
		OnRep_ProgressUpdate();
		vanRef->FillFuelTank(deltaSeconds);
		UpdateWeight();
		UpdateWidget(GetCurrentFuel());
		TraceCap(true);

		if (progress >= actionDuration)
		{
			Complete();
			vanRef = nullptr;
			SetActionDuration(0.f);
		}
		else if (!vanRef || vanRef->IsFuelTankFull())
		{
			Deactivate();
			SetActionDuration(0.f);
		}
	}
}

void AFuel::Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	if (interactComponent && bIsHeld && !bIsComplete)
	{
		userComponent = interactComponent;
		TraceCap(false);
	}
}

void AFuel::TraceCap(const bool bOnlyTrace)
{
	FHitResult hitResult;

	if (userComponent->GetTraceResult(180.f, hitResult) && hitResult.GetActor())
	{
		AFuelCap* hitCap = Cast<AFuelCap>(hitResult.GetActor());
		if (hitCap && hitCap->bIsOpen)
		{
			if (!bOnlyTrace)
			{
				vanRef = hitCap->vanRef;
				if (vanRef && !vanRef->IsFuelTankFull())
				{
					SetActionDuration(amountOfFuel);
					Activate();
					userComponent->bIsInteracting = true;
					userComponent->NotifyItemSettingsUpdate(this);
				}
			}
		}
		else
		{
			Deactivate();
		}
	}
	else
	{
		Deactivate();
	}
}

void AFuel::Interrupt_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	Super::Interrupt_Implementation(interactor, interactComponent);

	if (interactComponent)
	{
		if (bIsHeld)
		{
			interactComponent->bIsInteracting = false;

			vanRef = nullptr;
			SetActionDuration(0.f);
		}
	}
}

void AFuel::Deactivate_Implementation()
{
	Super::Deactivate_Implementation();

	vanRef = nullptr;
}

void AFuel::NotifyDrop_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	Super::NotifyDrop_Implementation(interactor, interactComponent);

	vanRef = nullptr;
	SetActionDuration(0.f);
}

float AFuel::GetCurrentFuel()
{
	float currentFuel = FMath::Clamp(amountOfFuel - progress, 0.f, 12.f);
	return currentFuel;
}

void AFuel::UpdateWeight()
{
	float currentFuel = GetCurrentFuel();
	if (currentFuel <= 2.f)
		itemMesh->SetMassOverrideInKg(FName("None"), 20.f, true);
	else if (currentFuel > 2.f && currentFuel <= 6.f)
		itemMesh->SetMassOverrideInKg(FName("None"), 28.f, true);
	else if (currentFuel > 6.f)
		itemMesh->SetMassOverrideInKg(FName("None"), 36.f, true);
}

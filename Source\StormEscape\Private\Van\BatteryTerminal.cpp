#include "Van/BatteryTerminal.h"
#include "Net/UnrealNetwork.h"

ABatteryTerminal::ABatteryTerminal()
{
	SetRootComponent(itemMesh);
	actionDuration = 3.f;
	bIsLoose = false;
	bHasBatteryInstalled = true;

	progressType = EProgressType::Persistent;
}

void ABatteryTerminal::Interact_Implementation(AActor* interactor, UInteractComponent* interactComponent)
{
	if (bHasBatteryInstalled)
	{
		Super::Interact_Implementation(interactor, interactComponent);
	}
		
}

void ABatteryTerminal::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(ABatteryTerminal, bIsLoose);
	DOREPLIFETIME(ABatteryTerminal, bHasBatteryInstalled);
}

void ABatteryTerminal::BeginPlay()
{
	Super::BeginPlay();
}

void ABatteryTerminal::Complete_Implementation()
{
	bIsLoose = !bIsLoose;
	OnTerminalInteractedDelegate.Broadcast(bIsLoose);

	Super::Complete_Implementation();
}

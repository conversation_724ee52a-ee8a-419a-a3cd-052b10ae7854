﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "Blueprint/UserWidget.h"
#include "Core/StormEscapeGameInstance.h"
#include "LobbySearchPanel.generated.h"


class ULobbySearchPage;
struct FLobbySearchResult;
class UStormEscapeGameInstance;

/** Struct holding search filter parameters for lobby search. */
USTRUCT(BlueprintType)
struct FSearchFilter
{
	GENERATED_BODY()

	/** Maximum allowed ping for a server to be shown. 0 means no filter. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Search Filter")
	int32 MaxPing = 0;

	/** Maximum allowed number of players in a server. */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Search Filter")
	int32 MaxNumPlayers = 8;
};

/**
 * 
 */
UCLASS()
class STORMESCAPE_API ULobbySearchPanel : public UCommonActivatableWidget
{
	GENERATED_BODY()

public:

	UFUNCTION(BlueprintCallable)
	void GenerateTestLobbies(int32 NumLobbies);

	UFUNCTION(BlueprintCallable)
	void DisplayLobbies(const TArray<FLobbySearchResult>& Lobbies);

	UFUNCTION(BlueprintCallable)
	void SetSearchText(const FString& NewSearchText);

	UFUNCTION(BlueprintCallable)
	void SetSearchFilter(const FSearchFilter& NewFilter);

	UFUNCTION(BlueprintImplementableEvent)
	void OnPagesRefreshed(int32 NumOfPages);

	UPROPERTY(EditAnywhere)
	TSubclassOf<ULobbySearchPage> PageClass;

	UPROPERTY(BlueprintReadOnly, Category="Filters")
	FSearchFilter SearchFilter;

	UPROPERTY(BlueprintReadOnly, Category="Filters")
	FString SearchText;

	UPROPERTY(BlueprintReadOnly)
	TArray<FLobbySearchResult> AllLobbies; // All received lobbies

	UPROPERTY(BlueprintReadOnly)
	TArray<FLobbySearchResult> FilteredLobbies; // After filter struct

	UPROPERTY(BlueprintReadOnly)
	TArray<FLobbySearchResult> DisplayedLobbies; // After name filter

	UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
	class UWidgetSwitcher* PageSwitcher;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Server Sorting")
	int32 MaxGoodPingLimit = 60;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Server Sorting")
	int32 MaxAveragePingLimit = 120;

protected:

	virtual void NativeConstruct() override;

	UFUNCTION()
	void OnLobbiesResultsReceived(const TArray<FLobbySearchResult>& Results);

	UPROPERTY(EditDefaultsOnly, Category = "Pagination")
	int32 ResultsPerPage = 10;

	/** Main pipeline: filter by struct, then by name, then sort and display. */
	void ApplyAllFiltersAndDisplay();

	/** Filter by struct settings (ping, max players, etc). */
	void FilterLobbiesBySettings();

	/** Filter by search text. */
	void FilterLobbiesByName();

	/** Sort lobbies by ping range: Good, then Average, then Bad. */
	void SortLobbiesByPingRanges(TArray<FLobbySearchResult>& Lobbies) const;

	/** Display paginated lobbies. */
	void DisplayLobbiesInternal(const TArray<FLobbySearchResult>& Lobbies);

	// Filter helpers
	bool PassesPingFilter(const FLobbySearchResult& Lobby) const;
	bool PassesMaxPlayersFilter(const FLobbySearchResult& Lobby) const;
	bool PassesAllFilters(const FLobbySearchResult& Lobby) const;

private:

	UStormEscapeGameInstance* GameInstance;

};

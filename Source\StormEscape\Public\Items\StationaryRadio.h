// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Items/BaseRadioItem.h"
#include "Items/BaseRemoteItem.h"
#include "CableComponent.h"
#include "Components/ArrowComponent.h"
#include "PhysicsEngine/PhysicsConstraintComponent.h"
#include "StationaryRadio.generated.h"

/**
 *
 */
UCLASS()
class STORMESCAPE_API AStationaryRadio : public ABaseRadioItem
{
	GENERATED_BODY()

public:
	AStationaryRadio();

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

protected:
	virtual void BeginPlay() override;

	virtual AActor* GetDefaultOwner() const override { return microphone ? microphone->GetOwner() : nullptr; }

	UFUNCTION(BlueprintCallable)
	void UpdateCableConstraints();

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UCableComponent* cableComponent;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UPhysicsConstraintComponent* physicsConstraint;

	UPROPERTY(EditAnywhere, BlueprintReadWrite)
	UArrowComponent* micSocket;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Microphone")
	TSubclassOf<ABaseRemoteItem> micClass;

	/* Equilibrium length to aim for when the mic isn't being held */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Microphone")
	float cordRestLength;

	/* Max stretch length that a player can carry the mic away from the radio */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Microphone")
	float cordMaxLength;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Microphone")
	float cordPullStrength;

	UPROPERTY(Replicated)
	ABaseRemoteItem* microphone;

private:
	UFUNCTION()
	void OnMicCarrierChanged(AActor* micOwner);
};

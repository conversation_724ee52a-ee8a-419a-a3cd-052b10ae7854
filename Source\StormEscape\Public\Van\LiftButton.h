#pragma once

#include "CoreMinimal.h"
#include "Items/BaseVehiclePart.h"
#include "LiftButton.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnLiftButtonPressed, bool, bOpen);

UCLASS()
class STORMESCAPE_API ALiftButton : public ABaseVehiclePart
{
	GENERATED_BODY()
	
public:
	ALiftButton();

	FOnLiftButtonPressed OnLiftButtonPressedDelegate;

	void OpenDoor();

protected:
	virtual void Use_Implementation() override;

	UFUNCTION(BlueprintImplementableEvent)
	void UpdateButtonMaterial(const bool bReady);

private:
	bool bIsOn;
};

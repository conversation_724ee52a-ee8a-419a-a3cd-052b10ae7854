﻿#pragma once

#include "CoreMinimal.h"
#include "CommonActivatableWidget.h"
#include "Blueprint/UserWidget.h"
#include "Core/StormEscapeGameInstance.h"
#include "CreateRoomSettings.generated.h"

class UEditableTextBox;
class UOptionSelector;
class UButton;

UCLASS()
class STORMESCAPE_API UCreateRoomSettings : public UCommonActivatableWidget
{
	GENERATED_BODY()

public:
	
	UFUNCTION(BlueprintCallable)
	void CreateRoom();
	
	UFUNCTION(BlueprintImplementableEvent, Category="Room")
	void InvalidName(const FText& ViolationText);

protected:

	virtual void NativeConstruct() override;

	UPROPERTY(BlueprintReadOnly ,meta = (BindWidget))
	UOptionSelector* PrivateRoomSelector;

	UPROPERTY(BlueprintReadOnly ,meta = (BindWidget))
	UOptionSelector* DifficultySelector;

	UPROPERTY(BlueprintReadOnly ,meta = (BindWidget))
	UOptionSelector* MaxPlayersSelector;

	UPROPERTY(BlueprintReadOnly ,meta = (BindWidget))
	UOptionSelector* PreferredLanguageSelector;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Room")
	FString LobbyNameRulesPattern;

private:

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category="Room", meta=(AllowPrivateAccess="true"))
	FCreateLobby RoomOptions;

	// Callbacks
	UFUNCTION()
	void OnPrivateRoomOptionChanged(int32 Index, FText Value);

	UFUNCTION()
	void OnDifficultyOptionChanged(int32 Index, FText Value);

	UFUNCTION()
	void OnMaxPlayersOptionChanged(int32 Index, FText Value);

	UFUNCTION()
	void OnLanguageOptionChanged(int32 Index, FText Value);

	void InitializeOptions();
};

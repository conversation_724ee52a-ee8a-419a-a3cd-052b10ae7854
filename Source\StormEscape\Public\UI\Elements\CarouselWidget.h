// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "CarouselWidget.generated.h"

/**
 * 
 */
UCLASS()
class STORMESCAPE_API UCarouselWidget : public UUserWidget
{
	GENERATED_BODY()
	

public:
	UFUNCTION(BlueprintCallable)
	void Setup(const TArray<FName>& InOptions, int DefaultOption = 0);

	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnSetup")
	void K2_OnSetup(const FName& DefaultOption);

	UFUNCTION(BlueprintImplementableEvent, DisplayName="OnOptionChanged")
	void K2_OnOptionChanged(const FName& OptionTag);

	UFUNCTION(BlueprintPure)
	FName GetSelectedOption() const { return Options.IsValidIndex(SelectedIndex) ? Options[SelectedIndex] : FName(); }

protected:
	UFUNCTION(BlueprintCallable)
	void Previous();

	UFUNCTION(BlueprintCallable)
	void Next();

	UPROPERTY(EditInstanceOnly, BlueprintReadOnly, Category = "Settings", meta = (ExposeOnSpawn = true))
	bool bLoopAround = false;

private:
	int SelectedIndex;

	int MaxIndex;

	TArray<FName> Options;
};

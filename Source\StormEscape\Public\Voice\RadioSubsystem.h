// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/WorldSubsystem.h"
#include "OdinAudioCapture.h"
#include "Voice/VoiceImportantTypes.h"
#include "RadioSubsystem.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogRadio, Log, All);

UCLASS()
class STORMESCAPE_API URadioSubsystem : public UWorldSubsystem
{
	GENERATED_BODY()
	
public:
	virtual void OnWorldBeginPlay(UWorld& InWorld) override;

	void RegisterReceiver(AActor* receiver);

	void UnregisterReceiver(AActor* receiver);

	TArray<AActor*> GetReceivers() const { return receivers; }

	void RegisterLocalSpeaker(AActor* speaker, int64 peerID, UOdinAudioCapture* audioCapture);

	void RegisterSpeaker(AActor* speaker, int64 peerID);

	void UnregisterSpeaker(AActor* speaker);

	int64 GetPeerFromSpeaker(AActor* speaker) const;

	int64 GetTransmitterID() const { return transmitterID; }

	bool IsTransmitting() const { return transmitterID == INDEX_NONE; }

	void NotifyTransmissionStart(int64 peerID);

	void NotifyTransmissionEnd(int64 peerID);


	DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnRadioTransmittersChanged, int64, peerID);

	FOnRadioTransmittersChanged onTransmittersChangedDelegate;
	
protected:
	void SetTransmitterID(int64 peerID);

	void OnTransmissionChanged();

	FTimerHandle delayTimer;

	float radioDelay;

	TArray<AActor*> receivers;

	TMap<AActor*, int64> speakerToPeerMap;

	int64 localPeerID;

	UOdinAudioCapture* localCapture;

	TArray<int64> transmissionQueue;

	int64 transmitterID;
};



[/Script/EngineSettings.GeneralProjectSettings]
ProjectID=944DB6C1412F349E3719C7BE7C804E00
ProjectVersion=v0.0.4

[/Script/UnrealEd.ProjectPackagingSettings]
Build=IfProjectHasCode
BuildConfiguration=PPBC_Development
BuildTarget=
FullRebuild=False
ForDistribution=False
IncludeDebugFiles=False
BlueprintNativizationMethod=Disabled
bIncludeNativizedAssetsInProjectGeneration=False
bExcludeMonolithicEngineHeadersInNativizedCode=False
UsePakFile=True
bUseIoStore=True
bUseZenStore=False
bMakeBinaryConfig=False
bGenerateChunks=False
bGenerateNoChunks=False
bChunkHardReferencesOnly=False
bForceOneChunkPerFile=False
MaxChunkSize=0
bBuildHttpChunkInstallData=False
HttpChunkInstallDataDirectory=(Path="")
WriteBackMetadataToAssetRegistry=Disabled
bWritePluginSizeSummaryJsons=False
bCompressed=True
PackageCompressionFormat=Oodle
bForceUseProjectCompressionFormatIgnoreHardwareOverride=False
PackageAdditionalCompressionOptions=
PackageCompressionMethod=Kraken
PackageCompressionLevel_DebugDevelopment=4
PackageCompressionLevel_TestShipping=4
PackageCompressionLevel_Distribution=7
PackageCompressionMinBytesSaved=1024
PackageCompressionMinPercentSaved=5
bPackageCompressionEnableDDC=False
PackageCompressionMinSizeToConsiderDDC=0
HttpChunkInstallDataVersion=
IncludePrerequisites=True
IncludeAppLocalPrerequisites=False
bShareMaterialShaderCode=True
bDeterministicShaderCodeOrder=False
bSharedMaterialNativeLibraries=True
ApplocalPrerequisitesDirectory=(Path="")
IncludeCrashReporter=False
InternationalizationPreset=English
-CulturesToStage=en
+CulturesToStage=en
LocalizationTargetCatchAllChunkId=0
bCookAll=False
bCookMapsOnly=False
bTreatWarningsAsErrorsOnCook=False
bSkipEditorContent=False
bSkipMovies=False
-IniKeyDenylist=KeyStorePassword
-IniKeyDenylist=KeyPassword
-IniKeyDenylist=DebugKeyStorePassword
-IniKeyDenylist=DebugKeyPassword
-IniKeyDenylist=rsa.privateexp
-IniKeyDenylist=rsa.modulus
-IniKeyDenylist=rsa.publicexp
-IniKeyDenylist=aes.key
-IniKeyDenylist=SigningPublicExponent
-IniKeyDenylist=SigningModulus
-IniKeyDenylist=SigningPrivateExponent
-IniKeyDenylist=EncryptionKey
-IniKeyDenylist=DevCenterUsername
-IniKeyDenylist=DevCenterPassword
-IniKeyDenylist=IOSTeamID
-IniKeyDenylist=SigningCertificate
-IniKeyDenylist=MobileProvision
-IniKeyDenylist=IniKeyDenylist
-IniKeyDenylist=IniSectionDenylist
+IniKeyDenylist=KeyStorePassword
+IniKeyDenylist=KeyPassword
+IniKeyDenylist=DebugKeyStorePassword
+IniKeyDenylist=DebugKeyPassword
+IniKeyDenylist=rsa.privateexp
+IniKeyDenylist=rsa.modulus
+IniKeyDenylist=rsa.publicexp
+IniKeyDenylist=aes.key
+IniKeyDenylist=SigningPublicExponent
+IniKeyDenylist=SigningModulus
+IniKeyDenylist=SigningPrivateExponent
+IniKeyDenylist=EncryptionKey
+IniKeyDenylist=DevCenterUsername
+IniKeyDenylist=DevCenterPassword
+IniKeyDenylist=IOSTeamID
+IniKeyDenylist=SigningCertificate
+IniKeyDenylist=MobileProvision
+IniKeyDenylist=IniKeyDenylist
+IniKeyDenylist=IniSectionDenylist
-IniSectionDenylist=HordeStorageServers
-IniSectionDenylist=StorageServers
-IniSectionDenylist=/Script/AndroidFileServerEditor.AndroidFileServerRuntimeSettings
+IniSectionDenylist=HordeStorageServers
+IniSectionDenylist=StorageServers
+IniSectionDenylist=/Script/AndroidFileServerEditor.AndroidFileServerRuntimeSettings
+MapsToCook=(FilePath="/Game/StormEscape/Maps/MainMenu")
+MapsToCook=(FilePath="/Game/StormEscape/Maps/Lobby")
+MapsToCook=(FilePath="/Game/StormEscape/Maps/lvl_MechanicsGym")
+MapsToCook=(FilePath="/Game/StormEscape/Maps/BeautyCorner_Optimized")
+DirectoriesToAlwaysCook=(Path="/NNEDenoiser")
bRetainStagedDirectory=False
CustomStageCopyHandler=

[/Script/StormEscape.CustomUISettings]
LoadingScreen=/Script/UMG.WidgetBlueprintGeneratedClass'/Game/StormEscape/UI/Widgets/LoadingScreen/W_TransitionScreen.W_TransitionScreen_C'
ErrorWidgetClass=/Script/UMG.WidgetBlueprintGeneratedClass'/Game/StormEscape/UI/Widgets/Popups/W_ErrorPopup.W_ErrorPopup_C'
PauseMenuWidgetClass=/Script/UMG.WidgetBlueprintGeneratedClass'/Game/StormEscape/UI/Widgets/PauseMenu/W_PauseMenu.W_PauseMenu_C'
HostLoadingScreen=/Script/UMG.WidgetBlueprintGeneratedClass'/Game/StormEscape/UI/Widgets/LoadingScreen/W_HostLoadingScreen.W_HostLoadingScreen_C'

[/Script/StormEscape.CustomAudioSettings]
radioDelayEffect=/Game/StormEscape/Audio/Radio/RadioEffect_Delay.RadioEffect_Delay

[/Script/CommonInput.CommonInputSettings]
InputData=/Game/StormEscape/Input/Data/CUI_InputData.CUI_InputData_C

[CommonInputPlatformSettings_Windows CommonInputPlatformSettings]
DefaultInputType=MouseAndKeyboard
bSupportsMouseAndKeyboard=True
bSupportsTouch=False
bSupportsGamepad=True
DefaultGamepadName=XSX
bCanChangeGamepadType=True
+ControllerData=/Game/StormEscape/UI/Foundation/Platform/Input/XGamepad/CommonInput_Gamepad_X.CommonInput_Gamepad_X_C
+ControllerData=/Game/StormEscape/UI/Foundation/Platform/Input/KeyboardMouse/CommonInput_KeyboardMouse.CommonInput_KeyboardMouse_C


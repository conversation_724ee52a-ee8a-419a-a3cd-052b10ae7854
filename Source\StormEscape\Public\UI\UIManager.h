#pragma once

#include "CoreMinimal.h"
#include "UIManager.generated.h"

class UBaseMainLayerUI;
class UCommonActivatableWidget;
class UCommonActivatableWidgetStack;
class UInputMappingContext;

// Represents the different UI layers where widgets can be added.
// Used by the UIManager to route widgets to the correct widget stack.
UENUM(BlueprintType)
enum class EWidgetLayer : uint8
{
	// In-game UI elements that are non-intrusive and part of regular gameplay (e.g., health bars, minimap, ammo counter).
	GameUI UMETA(DisplayName = "Game UI"),

	// Menus that interrupt gameplay and may require exclusive input (e.g., pause menu, settings, main menu).
	Menu UMETA(DisplayName = "Menu"),

	// Non-blocking transient popups, such as notifications, alerts, or toasts. Input is not blocked behind them.
	Popup UMETA(DisplayName = "Popup"),

	// Blocking modal windows requiring player interaction (e.g., confirmation dialogs, error popups).
	Modal UMETA(DisplayName = "Modal")
};

UENUM(BlueprintType)
enum class EInputModeType : uint8
{
	UIOnly,
	GameOnly,
	GameAndUI
};

// Represents the current Enhanced Input mapping context state
UENUM(BlueprintType)
enum class EInputContextState : uint8
{
	// Game controls active (IMC_FirstPersonMappingContext)
	Game UMETA(DisplayName = "Game Controls"),

	// UI navigation controls active (IM_UIControls)
	UI UMETA(DisplayName = "UI Controls"),

	// No context set or unknown state
	None UMETA(DisplayName = "None")
};


UCLASS()
class STORMESCAPE_API UUIManager : public UObject
{
	GENERATED_BODY()

public:

	/** Called internally at BeginPlay in PlayerController */
	static void Initialize(APlayerController* PlayerController, UBaseMainLayerUI* MainLayerUIInstance);

	/** Adds widget to the stack, sets input mode and cursor */
	UFUNCTION(BlueprintCallable, Category = "UIManager" , meta = (WorldContext = "WorldContextObject"))
		static UCommonActivatableWidget* AddWidgetToStack(UObject* WorldContextObject,
		TSubclassOf<UCommonActivatableWidget> WidgetClass,
		EWidgetLayer TargetLayer,
		EInputModeType InputMode = EInputModeType::UIOnly,
		bool bShowMouseCursor = true,
		bool bClearCurrentStack = false);

	/** An easier Way to Set the player input.
	 basically it just calls the SetInputMode and MouseCursor on the local player controller */
	UFUNCTION(BlueprintCallable, Category = "UIManager" , meta = (WorldContext = "WorldContextObject"))
	static void SetInputMode(UObject* WorldContextObject,
		EInputModeType InputMode = EInputModeType::UIOnly,
		bool bShowMouseCursor = true, UUserWidget* Widget = nullptr);


	/* Returns the widget containing the layers stacks (MainBaseLayerUI) */
	UFUNCTION(BlueprintCallable, Category = "UIManager")
	UBaseMainLayerUI* GetMainLayerUI() const
	{
		return MainLayerUI.Get();
	}

	/** Manually trigger input context evaluation (useful for debugging or edge cases) */
	UFUNCTION(BlueprintCallable, Category = "UIManager")
	static void EvaluateInputContext();

	/** Force switch to game input context (IMC_FirstPersonMappingContext) */
	UFUNCTION(BlueprintCallable, Category = "UIManager")
	static void ForceGameInputContext();

	/** Force switch to UI input context (IM_UIControls) */
	UFUNCTION(BlueprintCallable, Category = "UIManager")
	static void ForceUIInputContext();

	/** Add UI input context while keeping game context active (alternative approach) */
	UFUNCTION(BlueprintCallable, Category = "UIManager")
	static void AddUIInputContextOverlay();

	/** Debug function to test if a specific input action is available */
	UFUNCTION(BlueprintCallable, Category = "UIManager")
	static void TestInputAction(const FString& ActionName);

	/** Debug function to check input focus and widget state */
	UFUNCTION(BlueprintCallable, Category = "UIManager")
	static void DebugInputState();

	/** Get the current input context state */
	UFUNCTION(BlueprintPure, Category = "UIManager")
	static EInputContextState GetCurrentInputContextState() { return CurrentInputContextState; }

private:

	static TWeakObjectPtr<UBaseMainLayerUI> MainLayerUI;
	static TWeakObjectPtr<APlayerController> CachedPC;

	// Enhanced Input mapping contexts
	static TWeakObjectPtr<UInputMappingContext> GameInputContext;
	static TWeakObjectPtr<UInputMappingContext> UIInputContext;

	// Current input context state tracking
	static EInputContextState CurrentInputContextState;

	static UCommonActivatableWidgetStack* GetTargetStack(EWidgetLayer Layer);

	/** Evaluate current UI state and switch input contexts if needed */
	static void UpdateInputContextForUIState();

	/** Switch to game input context (IMC_FirstPersonMappingContext) */
	static void SwitchToGameInputContext();

	/** Switch to UI input context (IM_UIControls) */
	static void SwitchToUIInputContext();

	/** Check if any UI layers (Menu, Popup, Modal) have active widgets */
	static bool HasActiveUIWidgets();

	/** Bind to widget destruction to detect when widgets are removed */
	static void BindToWidgetDestruction(UCommonActivatableWidget* Widget);
};

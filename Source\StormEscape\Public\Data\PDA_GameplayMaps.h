﻿// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DataAsset.h"
#include "PDA_GameplayMaps.generated.h"


USTRUCT(BlueprintType)
struct FPlayMapInfo
{
	GENERATED_BODY()
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	FString DisplayName = {};
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	UTexture2D* DisplayIcon = {};
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	FText Description = {};
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	FString TravelAddress = {};


};


/**
 * 
 */
UCLASS()
class STORMESCAPE_API UPDA_GameplayMaps : public UPrimaryDataAsset
{
	GENERATED_BODY()

public:
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly)
	TArray<FPlayMapInfo> PlayMap;
};

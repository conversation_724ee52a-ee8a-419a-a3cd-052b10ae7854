// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Items/BaseItem.h"
#include "BaseDeviceItem.generated.h"

/**
 * 
 */
UCLASS()
class STORMESCAPE_API ABaseDeviceItem : public ABaseItem
{
	GENERATED_BODY()
	
public:
	ABaseDeviceItem();

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

	void NotifyPickUp_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	void NotifyDrop_Implementation(AActor* interactor, UInteractComponent* interactComponent) override;

	bool HasCustomInput() const { return IsValid(customInputMapping); }

	void AddCustomInputMapping(AActor* interactor);

	void RemoveCustomInputMapping(AActor* interactor);

protected:
	virtual void BeginPlay() override;

	virtual bool CanActivate_Implementation() const override;

	virtual void Activate_Implementation() override;

	virtual bool ShouldProgress_Implementation() override;

	virtual void Deactivate_Implementation() override;

/* INPUT */
protected:
	virtual void BindCustomInputs(UEnhancedInputComponent* inputComponent) {}

	virtual void RemoveCustomInputs(UEnhancedInputComponent* inputComponent);

	TArray<FInputBindingHandle> customBindings;

	UPROPERTY(EditAnywhere, Category = "Properties|Input")
	UInputMappingContext* customInputMapping;

private:
	UFUNCTION(Client, Reliable)
	void Client_AddCustomInputMapping(AActor* interactor);

	UFUNCTION(Client, Reliable)
	void Client_RemoveCustomInputMapping(AActor* interactor);

	FTimerHandle decreaseTimer;

/* DURABILITY */
protected:
	virtual float GetConsume() const { return consumePerSecond; }

	UFUNCTION(BlueprintPure)
	bool IsDepleted() const { return FMath::IsNearlyZero(durability); }

	UFUNCTION()
	virtual void OnRep_DurabilityUpdate();

	UPROPERTY(BlueprintReadOnly, ReplicatedUsing = OnRep_DurabilityUpdate)
	float durability;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Durability")
	float maxDurability;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Durability")
	float activationCost;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Properties|Durability")
	float consumePerSecond;

	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnDurabilityChanged")
	void K2_OnDurabilityChanged(float newValue, float maxValue);

	UFUNCTION(BlueprintImplementableEvent, DisplayName = "OnDepleted")
	void K2_OnDepleted();

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void DecreaseDurability(float deltaSeconds);
};

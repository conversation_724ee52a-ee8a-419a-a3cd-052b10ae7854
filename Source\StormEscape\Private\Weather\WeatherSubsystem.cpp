// Fill out your copyright notice in the Description page of Project Settings.


#include "Weather/WeatherSubsystem.h"

DEFINE_LOG_CATEGORY(LogWeather)

void UWeatherSubsystem::SpawnTornado(TSubclassOf<ATornado> tornadoClass)
{
	if (tornadoClass)
	{
		const ATornado* defaultTornado = Cast<ATornado>(tornadoClass->GetDefaultObject());
		FVector Location = FVector::ForwardVector * defaultTornado->GetSpawnDistance();
		FActorSpawnParameters SpawnInfo;

		tornado = GetWorld()->SpawnActor<ATornado>(tornadoClass, Location, FRotator::ZeroRotator, SpawnInfo);
		tornado->StartMovement(FVector::ZeroVector);
		UE_LOG(LogWeather, Log, TEXT("Tornado spawned at %s"), *Location.ToString())
	}
}

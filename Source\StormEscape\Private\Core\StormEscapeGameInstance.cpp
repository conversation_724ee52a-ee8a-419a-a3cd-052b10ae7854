// Fill out your copyright notice in the Description page of Project Settings.

#include "Core/StormEscapeGameInstance.h"
#include "Settings/CustomUISettings.h"
#include "Blueprint/UserWidget.h"
#include "Kismet/GameplayStatics.h"
#include "Online/OnlineSessionNames.h"
#include "OnlineSessionSettings.h"
#include "BlueprintLibraries/UIHelperLibrary.h"
#include "GameFramework/Character.h"
#include "Private/OnlineUserCloudInterfaceSteam.h"
#include "UI/UIManager.h"


UStormEscapeGameInstance* UStormEscapeGameInstance::GetActive_GameInstance(UObject* WorldContextObject)
{
	UStormEscapeGameInstance* FoundGameInstance = nullptr;

	if (UWorld* FoundWorld = GEngine->GetWorldFromContextObject(WorldContextObject, EGetWorldErrorMode::LogAndReturnNull))
	{
		FoundGameInstance = FoundWorld->GetGameInstance<UStormEscapeGameInstance>();
	}
	return FoundGameInstance;
}

ACharacter* UStormEscapeGameInstance::FindCharacterByGuid(FGuid NewGUID)
{
	ACharacter* FoundCharacter = nullptr;
	if (!GuidToCharacterMap.Contains(NewGUID))
	{
		return nullptr;
	}

	FoundCharacter = *GuidToCharacterMap.Find(NewGUID);

	if (FoundCharacter)
	{
		return FoundCharacter;
	}
	return nullptr;
}

void UStormEscapeGameInstance::BeginLoadingScreen(const FString& String)
{
	if (loadingScreen)
	{
		loadingScreen->Toggle(true);
		return;
	}
	const UCustomUISettings* UISettings = GetDefault<UCustomUISettings>();
	if (UISettings->LoadingScreen)
	{
		UE_LOG(LogTemp, Warning, TEXT("Loading Screen Widget Found"));
		loadingScreen = CreateWidget<ULoadingScreenWidget>(GetWorld(), UISettings->LoadingScreen);
		GetGameViewportClient()->AddViewportWidgetContent(loadingScreen->TakeWidget(), 100);
		loadingScreen->Toggle(true);
	}
	else
	{
		UE_LOG(LogTemp, Warning, TEXT("Loading Screen Widget Not Found"));
	}
}

void UStormEscapeGameInstance::EndLoadingScreen(UWorld* World)
{
	UE_LOG(LogTemp, Warning, TEXT("EndLoadingScreen called"));
	if (HostloadingScreen)
	{
		HostloadingScreen->Toggle(false);
	}
	if (loadingScreen)
	{
		loadingScreen->Toggle(false);
	}
}

void UStormEscapeGameInstance::Init()
{
	Super::Init();

	IOnlineSubsystem* Subsystem = IOnlineSubsystem::Get();
	if (Subsystem)
	{
		SessionInterface = Subsystem->GetSessionInterface();

		if (SessionInterface.IsValid())
		{
			SessionInterface->OnCreateSessionCompleteDelegates.AddUObject(this, &UStormEscapeGameInstance::OnCreateSessionComplete);
			SessionInterface->OnFindSessionsCompleteDelegates.AddUObject(this, &UStormEscapeGameInstance::OnFindSessionsComplete);
			SessionInterface->OnJoinSessionCompleteDelegates.AddUObject(this, &UStormEscapeGameInstance::OnJoinSessionComplete);
			SessionInterface->OnDestroySessionCompleteDelegates.AddUObject(this, &UStormEscapeGameInstance::OnSessionDestroyed);
		}
	}

	if (GEngine)
	{
		GEngine->OnNetworkFailure().AddUObject(this, &UStormEscapeGameInstance::HandleNetworkFailure);
	}

	FCoreUObjectDelegates::PreLoadMap.AddUObject(this, &UStormEscapeGameInstance::BeginLoadingScreen);
	FCoreUObjectDelegates::PostLoadMapWithWorld.AddUObject(this, &UStormEscapeGameInstance::EndLoadingScreen);

	// Start periodic input context checking with a delay to ensure UIManager is ready
	// This ensures game controls are restored when menus close
	FTimerHandle StartupTimerHandle;
	GetWorld()->GetTimerManager().SetTimer(StartupTimerHandle, []()
	{
		UUIManager::StartPeriodicInputContextCheck();
		UE_LOG(LogTemp, Log, TEXT("StormEscapeGameInstance: Started periodic input context checking"));
	}, 1.0f, false);
}

void UStormEscapeGameInstance::HostLobbySession(const FCreateLobby& LobbyData)
{
	if (!SessionInterface.IsValid())
	{
		UE_LOG(LogTemp, Error, TEXT("SessionInterface invalid during CreateLobbySession."));
		ThrowErrorPopup("Network Error", "Failed to initialize network session. Please check your internet connection.");
		return;
	}

	DestroyExistingSessionIfAny();

	FOnlineSessionSettings SessionSettings;
	SessionSettings.bAllowJoinInProgress = true;
	SessionSettings.bIsDedicated = false;
	SessionSettings.bShouldAdvertise = !LobbyData.bIsPrivateRoom;
	SessionSettings.bUsesPresence = true;
	SessionSettings.NumPublicConnections = LobbyData.MaxPlayers;
	SessionSettings.NumPrivateConnections = LobbyData.MaxPlayers;
	SessionSettings.bUseLobbiesIfAvailable = true;

	// Determine LAN or Online
	IOnlineSubsystem* OnlineSubsystem = IOnlineSubsystem::Get();
	if (OnlineSubsystem)
	{
		FName SubsystemName = OnlineSubsystem->GetSubsystemName();
		SessionSettings.bIsLANMatch = (SubsystemName != FName("STEAM"));

		UE_LOG(LogTemp, Log, TEXT("Using Online Subsystem: %s | LAN: %s"), *SubsystemName.ToString(), SessionSettings.bIsLANMatch ? TEXT("True") : TEXT("False"));
	}
	else
	{
		SessionSettings.bIsLANMatch = true;
		UE_LOG(LogTemp, Warning, TEXT("No OnlineSubsystem found. Defaulting to LAN."));
	}

	// Set custom key-value pairs for session
	SessionSettings.Set(TEXT("LobbyName"), LobbyData.LobbyName, EOnlineDataAdvertisementType::ViaOnlineServiceAndPing);
	SessionSettings.Set(TEXT("Language"), LobbyData.PreferredLanguage, EOnlineDataAdvertisementType::ViaOnlineServiceAndPing);
	SessionSettings.Set(TEXT("CurrentPlayers"), 1, EOnlineDataAdvertisementType::ViaOnlineServiceAndPing);

	GameDifficulty = LobbyData.Difficulty;
	bPrivateGame = LobbyData.bIsPrivateRoom;

	
	// Actually create the session
	SessionInterface->CreateSession(0,"StormEscape", SessionSettings);

}

void UStormEscapeGameInstance::SearchForLobbies()
{
	if (!SessionInterface.IsValid())
	{
		ThrowErrorPopup("Network Error", "Cannot search for lobbies - network services unavailable.");
		return;
	}
	
	DestroyExistingSessionIfAny();
	
	SessionSearch = MakeShareable(new FOnlineSessionSearch());
	SessionSearch->MaxSearchResults = 10000;
	SessionSearch->QuerySettings.Set(SEARCH_PRESENCE , true, EOnlineComparisonOp::Equals);

	
	//Check if Steam is the active subsystem
	IOnlineSubsystem* OnlineSubsystem = IOnlineSubsystem::Get();
	if (OnlineSubsystem)
	{
		FName SubsystemName = OnlineSubsystem->GetSubsystemName();
		SessionSearch->bIsLanQuery = (SubsystemName != "STEAM");

		UE_LOG(LogTemp, Warning, TEXT("Online Subsystem: %s"), *SubsystemName.ToString());
	}
	else
	{
		SessionSearch->bIsLanQuery = true; // Default to LAN if no OSS
		UE_LOG(LogTemp, Warning, TEXT("No Online Subsystem found, defaulting to LAN."));
	}

	SessionInterface->FindSessions(0, SessionSearch.ToSharedRef());
}

void UStormEscapeGameInstance::JoinLobby(const FLobbySearchResult& LobbyData)
{
	if (!SessionInterface.IsValid()) 
	{
		ThrowErrorPopup("Network Error", "Cannot join lobby - network services unavailable.");
		return;
	}
	
	SessionInterface->AddOnJoinSessionCompleteDelegate_Handle(
		FOnJoinSessionCompleteDelegate::CreateUObject(this, &UStormEscapeGameInstance::OnJoinSessionComplete));

	SessionInterface->JoinSession(0, "StormEscape", LobbyData.SessionResult);
	SelectedLobby = {};
}

void UStormEscapeGameInstance::SetSelectedLobby(const FLobbySearchResult& LobbyData, UUserWidget* Widget)
{
	SelectedLobby = LobbyData;
	OnStormSessionSelected.Broadcast(LobbyData, Widget);
}

void UStormEscapeGameInstance::LeaveLobby()
{

	FNamedOnlineSession* ExistingSession = SessionInterface->GetNamedSession("StormEscape");
	if (!ExistingSession) return;

	const ENetMode NetMode = GetWorld()->GetNetMode();

	if (NetMode == NM_ListenServer)
	{
		UE_LOG(LogTemp, Log, TEXT("Host leaving. Destroying session."));
		SessionInterface->DestroySession("StormEscape"); // Triggers OnSessionDestroyed
	}
	else if (NetMode == NM_Client)
	{
		UE_LOG(LogTemp, Log, TEXT("Client leaving. Ending session and traveling to menu."));

		SessionInterface->DestroySession("StormEscape");

		// Clients don't get OnSessionDestroyed, so we travel immediately after ending
		if (APlayerController* PC = GetWorld()->GetFirstPlayerController())
		{
			PC->ClientTravel("/Game/StormEscape/Maps/MainMenu", ETravelType::TRAVEL_Absolute);
		}
	}
}

void UStormEscapeGameInstance::ThrowErrorPopup(FString head, FString body)
{
	UUIHelperLibrary::ShowErrorPopup(head, body, this);
}

void UStormEscapeGameInstance::KickAndReturnToMenu(FString Reason)
{
	bWasKicked = true;
	KickReason = Reason;
		SessionInterface->DestroySession("StormEscape");
}

void UStormEscapeGameInstance::DestroyExistingSessionIfAny()
{
	if (!SessionInterface.IsValid()) return;

	FNamedOnlineSession* ExistingSession = SessionInterface->GetNamedSession("StormEscape");
	if (ExistingSession)
	{
		UE_LOG(LogTemp, Warning, TEXT("Existing session 'StormEscape' found. Destroying..."));
		SessionInterface->DestroySession("StormEscape");
	}
}

FLobbyDisplayInfo UStormEscapeGameInstance::GetCurrentLobbyData()
{
	FLobbyDisplayInfo LobbyData;

	if (!SessionInterface.IsValid())
	{
		UE_LOG(LogTemp, Warning, TEXT("SessionInterface is invalid."));
		return LobbyData;
	}

	FNamedOnlineSession* Session = SessionInterface->GetNamedSession("StormEscape");
	if (!Session)
	{
		UE_LOG(LogTemp, Warning, TEXT("No session found with name StormEscape."));
		return LobbyData;
	}

	const FOnlineSessionSettings& Settings = Session->SessionSettings;

	// Get LobbyName
	FString LobbyNameStr;
	if (Settings.Get(FName("LobbyName"), LobbyNameStr))
	{
		LobbyData.LobbyName = LobbyNameStr;
	}
	
	LobbyData.MaxPlayers = Session->SessionSettings.NumPublicConnections;

	// Get Current Players
	int32 CurrentPlayers = 0;
	Settings.Get(FName("CurrentPlayers"), CurrentPlayers);
	LobbyData.CurrentPlayers = CurrentPlayers;

	//Get Language
	int32 PreferredLang = Settings.Get(FName("Language"), PreferredLang);
	LobbyData.PreferredLanguage = PreferredLang;

	return LobbyData;
}

void UStormEscapeGameInstance::OnCreateSessionComplete(FName SessionName, bool bWasSuccessful)
{
	OnStormSessionCreated.Broadcast(bWasSuccessful);

	if (bWasSuccessful)
	{
		UE_LOG(LogTemp, Warning, TEXT("OnCreateSessionComplete Success"));

		UWorld* World = GetWorld();
		if (!World)
		{
			UE_LOG(LogTemp, Error, TEXT("GetWorld() is null, cannot proceed with session start."));
			return;
		}

		if (loadingScreen)
		{
			loadingScreen->BlockNextLoadingScreen();

			HostloadingScreen = CreateWidget<ULoadingScreenWidget>(
				World, GetDefault<UCustomUISettings>()->HostLoadingScreen);

			if (HostloadingScreen)
			{
				if (UGameViewportClient* Viewport = GetGameViewportClient())
				{
					Viewport->AddViewportWidgetContent(HostloadingScreen->TakeWidget(), 100);
					HostloadingScreen->Toggle(true);
				}
			}
		}

		// Delay ServerTravel to allow UI updates and avoid race conditions
		FTimerHandle TravelTimerHandle;
		World->GetTimerManager().SetTimer(TravelTimerHandle, [this]()
		{
			UWorld* WorldInner = GetWorld();
			if (WorldInner)
			{
				WorldInner->ServerTravel("/Game/StormEscape/Maps/Lobby?listen");
			}
		}, HostTravelDelay, false);
	}
	else
	{
		ThrowErrorPopup("Session Error", "Failed to create game session. Please try again.");
	}
}

void UStormEscapeGameInstance::OnFindSessionsComplete(bool bWasSuccessful)
{
	TArray<FLobbySearchResult> FoundLobbies;
	int32 Index = 0;

	if (bWasSuccessful && SessionSearch.IsValid())
	{
		for (const FOnlineSessionSearchResult& Result : SessionSearch->SearchResults)
		{
			FLobbySearchResult Lobby;

			// Lobby name 
			FString FoundLobbyName;
			if (Result.Session.SessionSettings.Get(FName("LobbyName"), FoundLobbyName))
			{
				Lobby.LobbyInfo.LobbyName = FoundLobbyName;
			}
			else
			{
				Lobby.LobbyInfo.LobbyName = "Unknown";
			}

			Lobby.LobbyInfo.MaxPlayers = Result.Session.SessionSettings.NumPublicConnections;

			// Player Count
			int32 CurrentPlayers = 0;
			Result.Session.SessionSettings.Get(FName("CurrentPlayers"), CurrentPlayers);
			Lobby.LobbyInfo.CurrentPlayers = CurrentPlayers;

			//Language
			int32 Lang = 0;
			Result.Session.SessionSettings.Get(FName("Language"), Lang);
			Lobby.LobbyInfo.PreferredLanguage = Lang;

			Lobby.Ping = Result.PingInMs;
			Lobby.ServerIndex = Index++;
			Lobby.SessionResult = Result;

			UE_LOG(LogTemp, Warning, TEXT("Session [%d] - Current Players: %d | Max: %d | Open Public: %d"), 
				Lobby.ServerIndex,
				CurrentPlayers,
				Lobby.LobbyInfo.MaxPlayers,
				Result.Session.NumOpenPublicConnections);

			FoundLobbies.Add(Lobby);
		}
	}
	else
	{
		ThrowErrorPopup("Search Error", "Failed to search for available lobbies. Please check your network connection.");
	}

	OnStormSessionsFound.Broadcast(FoundLobbies);
}

void UStormEscapeGameInstance::OnJoinSessionComplete(FName SessionName, EOnJoinSessionCompleteResult::Type Result)
{
	SessionInterface->ClearOnJoinSessionCompleteDelegates(this);

	FLobbyDisplayInfo LobbyData = GetCurrentLobbyData();
	
	if (Result == EOnJoinSessionCompleteResult::Success)
	{
		FString ConnectString;
		if (SessionInterface->GetResolvedConnectString(SessionName, ConnectString))
		{
			APlayerController* PC = GetFirstLocalPlayerController();
			if (PC)
			{
				BeginLoadingScreen("");
				PC->ClientTravel(ConnectString, TRAVEL_Absolute);
			}
		}
	}
	else
	{
		// Session is full or join failed
		FString ErrorMessage;

		switch (Result)
		{
		case EOnJoinSessionCompleteResult::SessionIsFull:
			ErrorMessage = TEXT("The lobby is full.");
			break;
		case EOnJoinSessionCompleteResult::SessionDoesNotExist:
			ErrorMessage = TEXT("The lobby no longer exists.");
			break;
		case EOnJoinSessionCompleteResult::UnknownError:
		default:
			ErrorMessage = TEXT("Failed to join the session.");
			break;
		}

		UE_LOG(LogTemp, Warning, TEXT("Join failed: %s"), *ErrorMessage);
		ThrowErrorPopup("Failed to Join",ErrorMessage);
	}
}

void UStormEscapeGameInstance::OnSessionDestroyed(FName Name, bool bArg)
{

	UE_LOG(LogTemp, Warning, TEXT("OnSessionDestroyed"));

	//travel to main menu
	if (APlayerController* PlayerController = GetWorld()->GetFirstPlayerController())
	{
		PlayerController->ClientTravel("/Game/StormEscape/Maps/MainMenu", ETravelType::TRAVEL_Absolute);
	}
}

void UStormEscapeGameInstance::HandleNetworkFailure(UWorld* World, UNetDriver* NetDriver,
	ENetworkFailure::Type ErrorType, const FString& ErrorMessage)
{
	FString ErrorTypeString;

	switch (ErrorType)
	{
	case ENetworkFailure::NetDriverAlreadyExists:
		ErrorTypeString = TEXT("Network driver already exists.");
		break;
	case ENetworkFailure::NetDriverCreateFailure:
		ErrorTypeString = TEXT("Failed to create network driver.");
		break;
	case ENetworkFailure::NetDriverListenFailure:
		ErrorTypeString = TEXT("Failed to start listening.");
		break;
	case ENetworkFailure::ConnectionLost:
		ErrorTypeString = TEXT("Lost connection to server.");
		break;
	case ENetworkFailure::ConnectionTimeout:
		ErrorTypeString = TEXT("Connection timed out.");
		break;
	case ENetworkFailure::FailureReceived:
		ErrorTypeString = TEXT("Connection failure received from server.");
		break;
	case ENetworkFailure::OutdatedClient:
		ErrorTypeString = TEXT("Client is outdated.");
		break;
	case ENetworkFailure::OutdatedServer:
		ErrorTypeString = TEXT("Server is outdated.");
		break;
	case ENetworkFailure::PendingConnectionFailure:
		ErrorTypeString = TEXT("Failed to connect (likely full or invalid session).");
		break;
	case ENetworkFailure::NetGuidMismatch:
		ErrorTypeString = TEXT("GUID mismatch between client and server.");
		break;
	case ENetworkFailure::NetChecksumMismatch:
		ErrorTypeString = TEXT("Network checksum mismatch.");
		break;
	default:
		ErrorTypeString = TEXT("Unknown network error.");
		break;
	}
	bWasKicked = true;
	KickReason = ErrorTypeString + TEXT("\n") + ErrorMessage;
	UE_LOG(LogTemp, Warning, TEXT("Network Failure: %s"), *ErrorTypeString);
}

void UStormEscapeGameInstance::RegisterCharacterToGuid(FGuid GUID, ACharacter* Character)
{
	GuidToCharacterMap.Add(GUID, Character);
}


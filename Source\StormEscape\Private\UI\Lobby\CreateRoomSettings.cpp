﻿#include "UI/Lobby/CreateRoomSettings.h"

#include "BlueprintLibraries/UIHelperLibrary.h"
#include "UI/Lobby/OptionSelector.h"
#include "Components/EditableTextBox.h"
#include "Core/StormEscapeGameInstance.h"

void UCreateRoomSettings::NativeConstruct()
{
	Super::NativeConstruct();
	
	if (PrivateRoomSelector && !PrivateRoomSelector->OnOptionChanged.IsAlreadyBound(this, &UCreateRoomSettings::OnPrivateRoomOptionChanged))
	{
		PrivateRoomSelector->OnOptionChanged.AddDynamic(this, &UCreateRoomSettings::OnPrivateRoomOptionChanged);
	}

	if (DifficultySelector && !DifficultySelector->OnOptionChanged.IsAlreadyBound(this, &UCreateRoomSettings::OnDifficultyOptionChanged))
	{
		DifficultySelector->OnOptionChanged.AddDynamic(this, &UCreateRoomSettings::OnDifficultyOptionChanged);
	}

	if (MaxPlayersSelector && !MaxPlayersSelector->OnOptionChanged.IsAlreadyBound(this, &UCreateRoomSettings::OnMaxPlayersOptionChanged))
	{
		MaxPlayersSelector->OnOptionChanged.AddDynamic(this, &UCreateRoomSettings::OnMaxPlayersOptionChanged);
	}

	if (PreferredLanguageSelector && !PreferredLanguageSelector->OnOptionChanged.IsAlreadyBound(this, &UCreateRoomSettings::OnLanguageOptionChanged))
	{
		PreferredLanguageSelector->OnOptionChanged.AddDynamic(this, &UCreateRoomSettings::OnLanguageOptionChanged);
	}

	RoomOptions.LobbyName = "";

	InitializeOptions();
}

void UCreateRoomSettings::InitializeOptions()
{
	// Setup PrivateRoomSelector
	if (PrivateRoomSelector)
	{
		RoomOptions.bIsPrivateRoom = false;
		PrivateRoomSelector->ChangeOptions({ FText::FromString("No"), FText::FromString("Yes") }, 0);
	}

	// Setup DifficultySelector
	if (DifficultySelector)
	{
		RoomOptions.Difficulty = 1;
		// Example: Easy(0), Medium(1), Hard(2)
		DifficultySelector->ChangeOptions({ FText::FromString("Easy"), FText::FromString("Medium"), FText::FromString("Hard") }, 1);
	}

	// Setup MaxPlayersSelector
	if (MaxPlayersSelector)
	{
		RoomOptions.MaxPlayers = 4;
		TArray<FText> PlayerOptions;
		for (int32 i = 2; i <= 8; ++i)
		{
			PlayerOptions.Add(FText::FromString(FString::Printf(TEXT("%d Players"), i)));
		}
		MaxPlayersSelector->ChangeOptions(PlayerOptions, 4);
	}

	// Setup PreferredLanguageSelector
	if (PreferredLanguageSelector)
	{
		RoomOptions.PreferredLanguage = 0;
		PreferredLanguageSelector->ChangeOptions({ FText::FromString("English"), FText::FromString("Spanish"), FText::FromString("French") }, 0);
	}
}

void UCreateRoomSettings::OnPrivateRoomOptionChanged(int32 Index, FText Value)
{
	RoomOptions.bIsPrivateRoom = (Index == 1); // Yes = true
}

void UCreateRoomSettings::OnDifficultyOptionChanged(int32 Index, FText Value)
{
	RoomOptions.Difficulty = Index;
}

void UCreateRoomSettings::OnMaxPlayersOptionChanged(int32 Index, FText Value)
{
	RoomOptions.MaxPlayers = Index + 2; // Because first option is 2 players
}

void UCreateRoomSettings::OnLanguageOptionChanged(int32 Index, FText Value)
{
	RoomOptions.PreferredLanguage = Index;
}

void UCreateRoomSettings::CreateRoom()
{
	if (RoomOptions.LobbyName.IsEmpty())
	{
		InvalidName(FText::FromString(TEXT("Server name cannot be empty.")));
		return;
	}

	if (!UUIHelperLibrary::MatchesRegex(RoomOptions.LobbyName,LobbyNameRulesPattern))
	{
		InvalidName(FText::FromString(TEXT("Name must be 3–12 characters and can only include A–Z, 0–9, _ - . ,")));
		return;
	}
	
	// Valid — continue
	UStormEscapeGameInstance* GameInstance = UStormEscapeGameInstance::GetActive_GameInstance(this);
	if (GameInstance)
	{
		GameInstance->HostLobbySession(RoomOptions);
	}
}

#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "InteractionInterface.generated.h"

class UInteractComponent;

UINTERFACE(MinimalAPI)
class UInteractionInterface : public UInterface
{
	GENERATED_BODY()
};

class STORMESCAPE_API IInteractionInterface
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void Interact(AActor* interactor, UInteractComponent* interactComponent);

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void Interrupt(AActor* interactor, UInteractComponent* interactComponent);

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void NotifyStartAction(AActor* interactor, UInteractComponent* interactComponent);

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void NotifyStopAction(AActor* interactor, UInteractComponent* interactComponent);

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void NotifyFocus(AActor* interactor, UInteractComponent* interactComponent);

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void NotifyUnfocus(AActor* interactor, UInteractComponent* interactComponent);

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void NotifyPickUp(AActor* interactor, UInteractComponent* interactComponent);

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	void NotifyDrop(AActor* interactor, UInteractComponent* interactComponent);

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	bool CanShowProgress(AActor* otherActor) const;

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	float GetActionDuration();

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	float GetCurrentProgress();

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	float GetCurrentProgressNormalized();

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	bool IsAPickeableItem();

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	bool CanBeInteracted();

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	bool IsHeld();

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	bool RequiresTool();

	UFUNCTION(BlueprintCallable, BlueprintNativeEvent)
	UStaticMeshComponent* GetItemMesh();
};

// Fill out your copyright notice in the Description page of Project Settings.


#include "Voice/RadioSubsystem.h"
#include "Settings/CustomAudioSettings.h"

DEFINE_LOG_CATEGORY(LogRadio)

void URadioSubsystem::OnWorldBeginPlay(UWorld& InWorld)
{
	Super::OnWorldBeginPlay(InWorld);
	transmitterID = INDEX_NONE;

	const UCustomAudioSettings* audioSettings = GetDefault<UCustomAudioSettings>();
	if (audioSettings)
	{
		radioDelay = audioSettings->GetRadioDelay();
	}
}

void URadioSubsystem::RegisterReceiver(AActor* receiver)
{
	receivers.Add(receiver);
}

void URadioSubsystem::UnregisterReceiver(AActor* receiver)
{
	if (receivers.Contains(receiver))
	{
		receivers.Remove(receiver);
	}
}

void URadioSubsystem::RegisterLocalSpeaker(AActor* speaker, int64 peerID, UOdinAudioCapture* audioCapture)
{
	localPeerID = peerID;
	localCapture = audioCapture;
	if (localCapture)
	{
		localCapture->SetIsMuted(true);
	}

	RegisterSpeaker(speaker, peerID);
}

void URadioSubsystem::RegisterSpeaker(AActor* speaker, int64 peerID)
{
	if (speaker && peerID != INDEX_NONE)
	{
		speakerToPeerMap.FindOrAdd(speaker) = peerID;
	}
}

void URadioSubsystem::UnregisterSpeaker(AActor* speaker)
{
	if (speaker && speakerToPeerMap.Contains(speaker))
	{
		speakerToPeerMap.Remove(speaker);
	}
}

int64 URadioSubsystem::GetPeerFromSpeaker(AActor* speaker) const
{
	if (!speaker || !speakerToPeerMap.Contains(speaker))
	{
		return INDEX_NONE;
	}

	return speakerToPeerMap[speaker];
}

void URadioSubsystem::NotifyTransmissionStart(int64 peerID)
{
	if (peerID == INDEX_NONE)
	{
		UE_LOG(LogRadio, Error, TEXT("Invalid peer tried to start radio transmission."));
		return;
	}

	if (IsTransmitting())
	{
		SetTransmitterID(peerID);
	}
	else
	{
		transmissionQueue.AddUnique(peerID);
	}
}

void URadioSubsystem::NotifyTransmissionEnd(int64 peerID)
{
	if (peerID == INDEX_NONE)
	{
		UE_LOG(LogRadio, Error, TEXT("Invalid peer tried to end radio transmission."));
		return;
	}

	if (transmitterID == peerID)
	{
		if (transmissionQueue.IsEmpty())
		{
			SetTransmitterID(INDEX_NONE);
		}
		else
		{
			SetTransmitterID(transmissionQueue[0]);
			transmissionQueue.RemoveAt(0);
		}
	}
	else
	{
		transmissionQueue.Remove(peerID);
	}
}

void URadioSubsystem::SetTransmitterID(int64 peerID)
{
	transmitterID = peerID;
	FTimerDelegate delayDelegate = FTimerDelegate::CreateUObject(this, &URadioSubsystem::OnTransmissionChanged);
	GetWorld()->GetTimerManager().SetTimer(delayTimer, delayDelegate, radioDelay, false);
}

void URadioSubsystem::OnTransmissionChanged()
{
	onTransmittersChangedDelegate.Broadcast(transmitterID);
}

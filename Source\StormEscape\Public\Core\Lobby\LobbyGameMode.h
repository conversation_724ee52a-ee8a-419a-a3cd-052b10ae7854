﻿#pragma once

#include "CoreMinimal.h"
#include "Data/PDA_GameplayMaps.h"
#include "GameFramework/GameModeBase.h"
#include "Interfaces/OnlineSessionInterface.h"
#include "LobbyGameMode.generated.h"

// Forward declaration for map info struct
struct FPlayMapInfo;

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnMatchStarted);

/**
 * Game mode for the lobby, handling player connections, session management, and match start logic.
 */
UCLASS()
class STORMESCAPE_API ALobbyGameMode : public AGameModeBase
{
	GENERATED_BODY()

public:
	// Kick a player by their name
	UFUNCTION(BlueprintCallable)
	void KickPlayerByName(const FString& PlayerNameToKick) const;

	// Start the match and transition to the selected map
	UFUNCTION(BlueprintCallable)
	void StartMatch();

	//add a delegate to notify the UI for all players
	UPROPERTY(BlueprintAssignable, Category = "Match")
	FOnMatchStarted OnMatchStarted;

protected:
	// Override to handle pre-login checks
	virtual void PreLogin(const FString& Options, const FString& Address, const FUniqueNetIdRepl& UniqueId, FString& ErrorMessage) override;

	// Override to handle post-login logic
	virtual void PostLogin(APlayerController* NewPlayer) override;

	// Override to handle player logout
	virtual void Logout(AController* Exiting) override;

	// Override to initialize the game mode
	virtual void BeginPlay() override;

	virtual AActor* ChoosePlayerStart_Implementation(AController* Player) override;

private:
	// Cached session interface for managing online sessions
	IOnlineSessionPtr CachedSessionInterface;

	// Flag to indicate if the match is in progress
	bool bIsMatchInProgress = false;

	// Indicates if the lobby is currently full (exposed to Blueprint)
	UPROPERTY(BlueprintReadOnly, Category="Lobby", meta=(AllowPrivateAccess="true"))
	bool bFullLobby = false;

	// Retrieve the current session
	FNamedOnlineSession* GetSession() const;

	// Get the maximum number of players allowed
	int32 GetMaxPlayers() const;

	// Get the current number of players
	int32 GetCurrentPlayers() const;

	// Update the current player count in the session
	void UpdateCurrentPlayers(int32 NewCount) const;
};

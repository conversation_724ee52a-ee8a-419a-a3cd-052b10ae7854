﻿// Fill out your copyright notice in the Description page of Project Settings.


#include "Core/Lobby/LobbyPlayerState.h"
#include "Net/UnrealNetwork.h"

void ALobbyPlayerState::ServerSetReady_Implementation(bool bNewReady)
{
	bIsReady = bNewReady;
	OnRep_IsReady();
}

void ALobbyPlayerState::OnRep_IsReady()
{
	// Update UI
}

void ALobbyPlayerState::GetLifetimeReplicatedProps(TArray<class FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);
	
	DOREPLIFETIME(ALobbyPlayerState, bIsReady);

}
